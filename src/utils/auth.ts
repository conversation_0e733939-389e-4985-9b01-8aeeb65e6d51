'use server'

import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { getRedisValue, setRedisValue } from '@/utils/redis/client'

export interface AuthResult {
  authenticated: boolean
  userId: string | null
  clientId: string | null
  clientInfo?: {
    client_id: string
    username: string
    sector: string | null
    lang: string | null
    plan_type: string | null
  }
}

/**
 * Optimized authentication verification using RLS policies
 * Eliminates redundant supabase.auth.getUser() calls by leveraging RLS
 */
export async function verifyAuth(): Promise<AuthResult> {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Single query - <PERSON><PERSON> automatically filters by auth.uid()
    // No need for manual auth.getUser() call!
    const { data: clientData, error: clientError } = await supabase
      .from('clients')
      .select('client_id, username, sector, lang, plan_type, auth_id')
      .single()

    if (clientError || !clientData) {
      return {
        authenticated: false,
        userId: null,
        clientId: null
      }
    }

    return {
      authenticated: true,
      userId: clientData.auth_id,
      clientId: clientData.client_id,
      clientInfo: {
        client_id: clientData.client_id,
        username: clientData.username,
        sector: clientData.sector,
        lang: clientData.lang,
        plan_type: clientData.plan_type
      }
    }
  } catch (error) {
    console.error('Error in verifyAuth:', error)
    return {
      authenticated: false,
      userId: null,
      clientId: null
    }
  }
}

/**
 * Cached authentication verification with Redis caching
 * Reduces database calls for frequently accessed auth data
 */
export async function verifyAuthCached(cacheKey?: string): Promise<AuthResult> {
  try {
    // Try to get from cache first
    if (cacheKey) {
      const cached = await getRedisValue<AuthResult>(`auth:${cacheKey}`)
      if (cached && cached.authenticated) {
        return cached
      }
    }

    // If not cached, get fresh data
    const authResult = await verifyAuth()

    // Cache successful auth for 5 minutes
    if (authResult.authenticated && cacheKey) {
      await setRedisValue(`auth:${cacheKey}`, authResult, 300)
    }

    return authResult
  } catch (error) {
    console.error('Error in verifyAuthCached:', error)
    return verifyAuth() // Fallback to non-cached version
  }
}

/**
 * Admin-specific authentication verification
 * Checks if user is the admin user
 */
export async function verifyAdminAuth(): Promise<AuthResult & { isAdmin: boolean }> {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get user data to check email
    const { data: userData, error: userError } = await supabase.auth.getUser()

    if (userError || !userData?.user) {
      return {
        authenticated: false,
        userId: null,
        clientId: null,
        isAdmin: false
      }
    }

    const adminEmail = process.env.ADMIN_USER
    const isAdmin = adminEmail && userData.user.email === adminEmail

    if (!isAdmin) {
      return {
        authenticated: true,
        userId: userData.user.id,
        clientId: null,
        isAdmin: false
      }
    }

    // For admin, still get client info if available
    const authResult = await verifyAuth()

    return {
      ...authResult,
      isAdmin: true
    }
  } catch (error) {
    console.error('Error in verifyAdminAuth:', error)
    return {
      authenticated: false,
      userId: null,
      clientId: null,
      isAdmin: false
    }
  }
}
