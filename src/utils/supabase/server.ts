'use server'

import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

// Create a client for server components
export async function createServerComponentSupabase() {
  const cookieStore = cookies()
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll: () => {
          return cookieStore.getAll().map((cookie) => ({
            name: cookie.name,
            value: cookie.value,
          }))
        },
        setAll: (cookiesList) => {
          try {
            cookiesList.forEach(({ name, value, options }) => {
              cookieStore.set({ name, value, ...options })
            })
          } catch (error) {
            // This can happen when attempting to set cookies during SSR
          }
        },
      },
    }
  )
} 