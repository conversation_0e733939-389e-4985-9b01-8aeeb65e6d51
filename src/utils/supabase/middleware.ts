import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  try {
    // Create a response object to modify
    const response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    })

    // Define public routes that don't need auth checks
    const publicRoutes = ['/login', '/signup', '/auth/', '/access', '/', '/not-found']
    const pathname = request.nextUrl.pathname
    const isPublicRoute = publicRoutes.some(route =>
      pathname === route || pathname.startsWith(route)
    )

    // Skip auth check for public routes to improve performance
    if (isPublicRoute) {
      return response
    }

    // Create supabase client with cookies from request
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            // Setting cookies on the response
            response.cookies.set({
              name,
              value,
              ...options,
            })
          },
          remove(name: string, options: CookieOptions) {
            // Removing cookies from the response
            response.cookies.set({
              name,
              value: '',
              ...options,
              maxAge: 0,
            })
          },
        },
      }
    )

    // Refresh session if expired or check if it's valid
    try {
      const { error } = await supabase.auth.getUser()

      if (error) {
        // Optimized cookie cleanup - target specific Supabase cookie names
        const supabaseCookieNames = [
          'sb-access-token',
          'sb-refresh-token',
          'sb-auth-token',
          'sb-provider-token',
          'sb-provider-refresh-token'
        ]

        supabaseCookieNames.forEach(cookieName => {
          if (request.cookies.get(cookieName)) {
            response.cookies.set({
              name: cookieName,
              value: '',
              maxAge: 0,
            })
          }
        })
      }
    } catch (authError) {
      console.error('Auth error in middleware:', authError)
    }

    return response
  } catch (error) {
    console.error('Error in updateSession:', error)
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    })
  }
}
