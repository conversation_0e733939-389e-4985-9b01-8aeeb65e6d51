'use server'

import { deleteRedisValue } from '@/utils/redis/client'
import {
  <PERSON>C<PERSON>,
  <PERSON>aqCache,
  ClientInfoCache,
  DashboardCache
} from '@/utils/cache'

/**
 * Manual cache invalidation utilities for specific events
 * Use these when you know data has changed and want immediate cache refresh
 */

/**
 * Invalidate all dashboard-related cache when usage is updated
 * Call this every 100 messages when usage_used is incremented
 */
export async function invalidateUsageCache(clientId: string): Promise<void> {
  try {
    console.log(`Invalidating usage cache for client: ${clientId}`)

    // Invalidate dashboard cache (contains usage data)
    await DashboardCache.invalidateDashboardData(clientId)

    // Also invalidate client info cache if it contains usage data
    await ClientInfoCache.invalidateClientInfo(clientId)

    console.log(`Usage cache invalidated successfully for client: ${clientId}`)
  } catch (error) {
    console.error('Error invalidating usage cache:', error)
  }
}

/**
 * Invalidate all subscription-related cache when user upgrades/downgrades plan
 * Call this when plan_type or next_billing_date changes
 */
export async function invalidateSubscriptionCache(clientId: string): Promise<void> {
  try {
    console.log(`Invalidating subscription cache for client: ${clientId}`)

    // Invalidate dashboard cache (contains subscription data)
    await DashboardCache.invalidateDashboardData(clientId)

    // Invalidate client info cache (contains plan_type)
    await ClientInfoCache.invalidateClientInfo(clientId)

    console.log(`Subscription cache invalidated successfully for client: ${clientId}`)
  } catch (error) {
    console.error('Error invalidating subscription cache:', error)
  }
}

/**
 * Invalidate FAQ cache when FAQs are added/updated/deleted
 * Call this after FAQ operations
 */
export async function invalidateFaqCache(clientId: string): Promise<void> {
  try {
    console.log(`Invalidating FAQ cache for client: ${clientId}`)

    await FaqCache.invalidateFaqs(clientId)

    // Also invalidate knowledge stats since FAQ count changed
    await deleteRedisValue(`knowledge:stats:${clientId}`)

    console.log(`FAQ cache invalidated successfully for client: ${clientId}`)
  } catch (error) {
    console.error('Error invalidating FAQ cache:', error)
  }
}

/**
 * Invalidate photo cache when photos are uploaded/deleted
 * Call this after photo operations
 */
export async function invalidatePhotoCache(clientId: string): Promise<void> {
  try {
    console.log(`Invalidating photo cache for client: ${clientId}`)

    await PhotoCache.invalidatePhotos(clientId)

    // Also invalidate knowledge stats since photo count changed
    await deleteRedisValue(`knowledge:stats:${clientId}`)

    console.log(`Photo cache invalidated successfully for client: ${clientId}`)
  } catch (error) {
    console.error('Error invalidating photo cache:', error)
  }
}

/**
 * Invalidate client profile cache when user updates profile info
 * Call this when username, sector, or language changes
 */
export async function invalidateProfileCache(clientId: string): Promise<void> {
  try {
    console.log(`Invalidating profile cache for client: ${clientId}`)

    // Invalidate client info cache
    await ClientInfoCache.invalidateClientInfo(clientId)

    // Also invalidate dashboard cache as it contains client info
    await DashboardCache.invalidateDashboardData(clientId)

    console.log(`Profile cache invalidated successfully for client: ${clientId}`)
  } catch (error) {
    console.error('Error invalidating profile cache:', error)
  }
}

/**
 * Nuclear option: Invalidate ALL cache for a specific client
 * Use this sparingly, only when you're unsure what changed
 */
export async function invalidateAllClientCache(clientId: string): Promise<void> {
  try {
    console.log(`Invalidating ALL cache for client: ${clientId}`)

    await Promise.all([
      DashboardCache.invalidateDashboardData(clientId),
      ClientInfoCache.invalidateClientInfo(clientId),
      FaqCache.invalidateFaqs(clientId),
      PhotoCache.invalidatePhotos(clientId)
    ])

    console.log(`All cache invalidated successfully for client: ${clientId}`)
  } catch (error) {
    console.error('Error invalidating all client cache:', error)
  }
}

/**
 * Get all cache keys for a specific client (for debugging/monitoring)
 */
export function getAllCacheKeys(clientId: string): string[] {
  return [
    DashboardCache.getCacheKey(clientId),        // dashboard:data:client-123
    ClientInfoCache.getCacheKey(clientId),       // client:info:client-123
    FaqCache.getListCacheKey(clientId),          // faqs:list:client-123
    FaqCache.getCountCacheKey(clientId),         // faqs:count:client-123
    PhotoCache.getCacheKey(clientId),            // photos:all:client-123
    `knowledge:stats:${clientId}`                // knowledge:stats:client-123
  ]
}

/**
 * Batch invalidate multiple cache keys (for advanced use cases)
 */
export async function batchInvalidateKeys(keys: string[]): Promise<void> {
  try {
    console.log(`Batch invalidating ${keys.length} cache keys`)

    const promises = keys.map(key => deleteRedisValue(key))
    await Promise.all(promises)

    console.log(`Batch invalidation completed for ${keys.length} keys`)
  } catch (error) {
    console.error('Error in batch invalidation:', error)
  }
}

/**
 * Example usage in your application:
 *
 * // When user sends 100th message (usage update)
 * await invalidateUsageCache(clientId)
 *
 * // When user upgrades plan
 * await invalidateSubscriptionCache(clientId)
 *
 * // When user adds/edits FAQ
 * await invalidateFaqCache(clientId)
 *
 * // When user uploads photo
 * await invalidatePhotoCache(clientId)
 *
 * // When user updates profile
 * await invalidateProfileCache(clientId)
 */
