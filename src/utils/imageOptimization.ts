/**
 * Cloudflare Images Optimization Utility
 *
 * This utility provides image optimization using Cloudflare Images as a proxy
 * while keeping original images stored in Supabase.
 *
 * Cost: ~$0.03-0.30/month for typical usage
 * Benefits: WebP/AVIF conversion, global CDN, automatic resizing
 */

// Cloudflare Images configuration
const CLOUDFLARE_ACCOUNT_ID = process.env.NEXT_PUBLIC_CLOUDFLARE_ACCOUNT_ID || 'your-account-id'
const CLOUDFLARE_IMAGES_DOMAIN = `https://imagedelivery.net/${CLOUDFLARE_ACCOUNT_ID}`

// Alternative: Cloudflare Worker for image optimization (if direct fetch doesn't work)
const CLOUDFLARE_WORKER_URL = process.env.NEXT_PUBLIC_CLOUDFLARE_WORKER_URL || ''

// Image optimization settings
interface ImageOptions {
  width?: number
  height?: number
  quality?: number
  format?: 'webp' | 'avif' | 'jpeg' | 'png' | 'auto'
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad'
}

/**
 * Optimize image URL using Cloudflare Images
 *
 * @param supabaseUrl - Original Supabase image URL
 * @param options - Optimization options
 * @returns Optimized image URL or original URL if optimization not needed
 */
export const optimizeImage = (
  supabaseUrl: string | null | undefined,
  options: ImageOptions = {}
): string => {


  // Return placeholder for invalid URLs
  if (!supabaseUrl || typeof supabaseUrl !== 'string') {
    return '/placeholder-image.svg'
  }

  // Default options
  const {
    width = 800,
    height = 600,
    quality = 85,
    format = 'auto',
    fit = 'scale-down'
  } = options

  // Don't optimize small images (thumbnails) to save costs
  if (width <= 100 && height <= 100) {
    return supabaseUrl
  }

  // Don't optimize if neither worker nor account ID is configured
  if ((!CLOUDFLARE_WORKER_URL || CLOUDFLARE_WORKER_URL === '') &&
      (!CLOUDFLARE_ACCOUNT_ID || CLOUDFLARE_ACCOUNT_ID === 'your-account-id')) {
    console.warn('No Cloudflare optimization configured (need either Worker URL or Account ID), using original image')
    return supabaseUrl
  }

  try {
    // Method 1: Use Cloudflare Worker (most reliable)
    if (CLOUDFLARE_WORKER_URL) {
      const params = new URLSearchParams({
        url: supabaseUrl,
        w: width.toString(),
        h: height.toString(),
        q: quality.toString(),
        f: format
      })

      const workerUrl = `${CLOUDFLARE_WORKER_URL}?${params.toString()}`



      return workerUrl
    }

    // Method 2: Fallback to direct Cloudflare Images (if worker not configured)
    if (CLOUDFLARE_ACCOUNT_ID && CLOUDFLARE_ACCOUNT_ID !== 'your-account-id') {
      const encodedUrl = encodeURIComponent(supabaseUrl)
      const optimizedUrl = `${CLOUDFLARE_IMAGES_DOMAIN}/fetch/w=${width},h=${height}/${encodedUrl}`



      return optimizedUrl
    }

    // Fallback: No optimization available, return original
    return supabaseUrl
  } catch (error) {
    console.error('Error optimizing image URL:', error)
    return supabaseUrl
  }
}

/**
 * Optimize image for gallery main view
 * High quality, WebP format, responsive sizing
 */
export const optimizeGalleryImage = (supabaseUrl: string | null | undefined): string => {
  return optimizeImage(supabaseUrl, {
    width: 1200,
    height: 900,
    quality: 90,
    format: 'auto',
    fit: 'scale-down'
  })
}

/**
 * Optimize image for large thumbnails (photo list)
 * Medium quality, smaller size for faster loading
 */
export const optimizeThumbnail = (supabaseUrl: string | null | undefined): string => {
  return optimizeImage(supabaseUrl, {
    width: 300,
    height: 300,
    quality: 80,
    format: 'auto',
    fit: 'cover'
  })
}

/**
 * Optimize image for small thumbnails (search results, navigation)
 * Lower quality, very small size, keep original for cost efficiency
 */
export const optimizeSmallThumbnail = (supabaseUrl: string | null | undefined): string => {
  // For very small thumbnails, use original to save Cloudflare costs
  if (!supabaseUrl) return '/placeholder-image.svg'
  return supabaseUrl
}

/**
 * Get responsive image URLs for different screen sizes
 * Useful for responsive images with srcSet
 */
export const getResponsiveImageUrls = (supabaseUrl: string | null | undefined) => {
  if (!supabaseUrl) {
    return {
      mobile: '/placeholder-image.svg',
      tablet: '/placeholder-image.svg',
      desktop: '/placeholder-image.svg'
    }
  }

  return {
    mobile: optimizeImage(supabaseUrl, { width: 400, height: 300, quality: 75 }),
    tablet: optimizeImage(supabaseUrl, { width: 800, height: 600, quality: 85 }),
    desktop: optimizeImage(supabaseUrl, { width: 1200, height: 900, quality: 90 })
  }
}

/**
 * Check if image optimization should be used
 * Based on image dimensions and user preferences
 */
export const shouldOptimizeImage = (width: number, height: number): boolean => {
  // Only optimize larger images to save costs
  return width > 200 || height > 200
}

/**
 * Smart image URL selector
 * Automatically chooses between original and optimized based on size
 */
export const getSmartImageUrl = (
  supabaseUrl: string | null | undefined,
  width: number,
  height: number
): string => {
  if (!supabaseUrl) return '/placeholder-image.svg'

  if (shouldOptimizeImage(width, height)) {
    return optimizeImage(supabaseUrl, { width, height })
  }

  return supabaseUrl
}

/**
 * Preload critical images for better performance
 * Use this for above-the-fold images
 */
export const preloadImage = (url: string): void => {
  if (typeof window !== 'undefined') {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = url
    document.head.appendChild(link)
  }
}

/**
 * Test Cloudflare Images configuration
 * Use this to verify your setup is working
 */
export const testCloudflareImages = () => {
  const testUrl = 'https://ijmgtghlpmqfdywtlbnn.supabase.co/storage/v1/object/public/photos/test.jpg'
  const optimizedUrl = optimizeImage(testUrl, { width: 400, height: 300 })

  // Test function for development/debugging only

  return optimizedUrl
}

/**
 * Image optimization configuration
 * Adjust these settings based on your needs and budget
 */
export const IMAGE_CONFIG = {
  // Thumbnail settings (keep original for cost efficiency)
  thumbnail: {
    useOptimization: false,
    maxWidth: 100,
    maxHeight: 100
  },

  // Photo list settings (light optimization)
  photoList: {
    useOptimization: true,
    maxWidth: 300,
    maxHeight: 300,
    quality: 80
  },

  // Gallery settings (full optimization)
  gallery: {
    useOptimization: true,
    maxWidth: 1200,
    maxHeight: 900,
    quality: 90
  }
} as const
