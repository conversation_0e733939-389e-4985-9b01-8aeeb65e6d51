'use client';

/**
 * Get a value from Redis through the API
 * @param key The key to retrieve
 * @returns The value associated with the key, or null if not found
 */
export async function getRedisValue<T>(key: string): Promise<T | null> {
  try {
    const response = await fetch(`/api/redis?key=${encodeURIComponent(key)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error fetching Redis value:', errorData);
      return null;
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error(`Error getting Redis value for key ${key}:`, error);
    return null;
  }
}

/**
 * Set a value in Redis through the API
 * @param key The key to set
 * @param value The value to store
 * @param expireInSeconds Optional TTL in seconds
 * @returns true if successful, false otherwise
 */
export async function setRedisValue<T>(
  key: string, 
  value: T, 
  expireInSeconds?: number
): Promise<boolean> {
  try {
    const response = await fetch('/api/redis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ key, value, expireInSeconds }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error setting Redis value:', errorData);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error setting Redis value for key ${key}:`, error);
    return false;
  }
}

/**
 * Delete a value from Redis through the API
 * @param key The key to delete
 * @returns true if successful, false otherwise
 */
export async function deleteRedisValue(key: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/redis?key=${encodeURIComponent(key)}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error deleting Redis value:', errorData);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error deleting Redis value for key ${key}:`, error);
    return false;
  }
}

/**
 * Get a value from Redis hash through the API
 * @param hash The hash name
 * @param field The field in the hash
 * @returns The value associated with the field, or null if not found
 */
export async function getHashField<T>(
  hash: string,
  field: string
): Promise<T | null> {
  try {
    const response = await fetch(
      `/api/redis?hash=${encodeURIComponent(hash)}&field=${encodeURIComponent(field)}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error fetching Redis hash field:', errorData);
      return null;
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error(`Error getting Redis hash field ${hash}:${field}:`, error);
    return null;
  }
}

/**
 * Set a value in Redis hash through the API
 * @param hash The hash name
 * @param field The field in the hash
 * @param value The value to store
 * @returns true if successful, false otherwise
 */
export async function setHashField<T>(
  hash: string,
  field: string,
  value: T
): Promise<boolean> {
  try {
    const response = await fetch('/api/redis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ hash, field, value }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error setting Redis hash field:', errorData);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error setting Redis hash field ${hash}:${field}:`, error);
    return false;
  }
}

/**
 * Get all fields and values from a Redis hash through the API
 * @param hash The hash name
 * @returns An object containing all fields and values, or null if not found
 */
export async function getHashAll<T>(hash: string): Promise<Record<string, T> | null> {
  try {
    const response = await fetch(`/api/redis?hash=${encodeURIComponent(hash)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error fetching Redis hash:', errorData);
      return null;
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error(`Error getting all Redis hash fields for ${hash}:`, error);
    return null;
  }
}
