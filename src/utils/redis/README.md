# Redis Integration

This directory contains utilities for interacting with Upstash Redis.

## Environment Variables

Add the following environment variables to your `.env.local` file:

```
UPSTASH_REDIS_REST_URL=your_upstash_redis_rest_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_rest_token
```

You can get these values from your Upstash Redis dashboard.

## Server-Side Usage

Import the Redis utilities in your server-side code:

```typescript
import { 
  getRedisValue, 
  setRedisValue, 
  deleteRedisValue,
  hasRedisKey,
  getHashField,
  setHashField,
  getHashAll
} from '@/utils/redis/client';

// Example: Store a value with a 1-hour expiration
await setRedisValue('user:123:session', { active: true }, 3600);

// Example: Retrieve a value
const session = await getRedisValue('user:123:session');

// Example: Store values in a hash
await setHashField('user:123', 'lastLogin', new Date().toISOString());

// Example: Get all fields from a hash
const userData = await getHashAll('user:123');
```

## Client-Side Usage

Import the client-side Redis API utilities:

```typescript
import { 
  getRedisValue, 
  setRedisValue, 
  deleteRedisValue,
  getHashField,
  setHashField,
  getHashAll
} from '@/utils/redis/clientApi';

// Example: Store a value with a 1-hour expiration
await setRedisValue('preferences', { theme: 'dark' }, 3600);

// Example: Retrieve a value
const preferences = await getRedisValue('preferences');

// Example: Store values in a hash
await setHashField('user-settings', 'notifications', true);

// Example: Get all fields from a hash
const allSettings = await getHashAll('user-settings');
```

## Security

The API routes are protected with authentication to ensure only authenticated users can access Redis data. Each request verifies the user's authentication status before proceeding.

## Error Handling

All Redis operations include error handling to prevent application crashes. Failed operations return `null` for get operations and `false` for set/delete operations.

## Rate Limiting

Consider implementing rate limiting for these API routes to prevent abuse. You can use Redis itself to implement rate limiting by tracking request counts per user.
