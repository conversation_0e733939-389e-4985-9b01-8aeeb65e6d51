import { Redis } from '@upstash/redis';

// Initialize Redis client with environment variables
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL || '',
  token: process.env.UPSTASH_REDIS_REST_TOKEN || '',
});

// Get Telegram secret key from environment variables
const TELEGRAM_SK = process.env.TELEGRAM_SK || '';

/**
 * Get a value from Redis
 * @param key The key to retrieve
 * @returns The value associated with the key, or null if not found
 */
export async function getRedisValue<T>(key: string): Promise<T | null> {
  try {
    // Check if this is a Telegram status key with placeholder
    let finalKey = key;
    if (key.includes(':tg:') && key.endsWith(':TELEGRAM_SK')) {
      // Replace the placeholder with the actual Telegram secret key
      finalKey = key.replace(':TELEGRAM_SK', `:${TELEGRAM_SK}`);
    }

    return await redis.get(finalKey);
  } catch (error) {
    console.error(`Error getting Redis value for key ${key}:`, error);
    return null;
  }
}

/**
 * Set a value in Redis
 * @param key The key to set
 * @param value The value to store
 * @param expireInSeconds Optional TTL in seconds
 * @returns true if successful, false otherwise
 */
export async function setRedisValue<T>(
  key: string,
  value: T,
  expireInSeconds?: number
): Promise<boolean> {
  try {
    // Check if this is a Telegram status key with placeholder
    let finalKey = key;
    if (key.includes(':tg:') && key.endsWith(':TELEGRAM_SK')) {
      // Replace the placeholder with the actual Telegram secret key
      finalKey = key.replace(':TELEGRAM_SK', `:${TELEGRAM_SK}`);
    }

    if (expireInSeconds) {
      await redis.set(finalKey, value, { ex: expireInSeconds });
    } else {
      await redis.set(finalKey, value);
    }
    return true;
  } catch (error) {
    console.error(`Error setting Redis value for key ${key}:`, error);
    return false;
  }
}

/**
 * Delete a value from Redis
 * @param key The key to delete
 * @returns true if successful, false otherwise
 */
export async function deleteRedisValue(key: string): Promise<boolean> {
  try {
    // Check if this is a Telegram status key with placeholder
    let finalKey = key;
    if (key.includes(':tg:') && key.endsWith(':TELEGRAM_SK')) {
      // Replace the placeholder with the actual Telegram secret key
      finalKey = key.replace(':TELEGRAM_SK', `:${TELEGRAM_SK}`);
    }

    await redis.del(finalKey);
    return true;
  } catch (error) {
    console.error(`Error deleting Redis value for key ${key}:`, error);
    return false;
  }
}

/**
 * Check if a key exists in Redis
 * @param key The key to check
 * @returns true if the key exists, false otherwise
 */
export async function hasRedisKey(key: string): Promise<boolean> {
  try {
    // Check if this is a Telegram status key with placeholder
    let finalKey = key;
    if (key.includes(':tg:') && key.endsWith(':TELEGRAM_SK')) {
      // Replace the placeholder with the actual Telegram secret key
      finalKey = key.replace(':TELEGRAM_SK', `:${TELEGRAM_SK}`);
    }

    return (await redis.exists(finalKey)) === 1;
  } catch (error) {
    console.error(`Error checking if Redis key ${key} exists:`, error);
    return false;
  }
}

/**
 * Set a value in Redis hash
 * @param hash The hash name
 * @param field The field in the hash
 * @param value The value to store
 * @returns true if successful, false otherwise
 */
export async function setHashField<T>(
  hash: string,
  field: string,
  value: T
): Promise<boolean> {
  try {
    await redis.hset(hash, { [field]: value });
    return true;
  } catch (error) {
    console.error(`Error setting Redis hash field ${hash}:${field}:`, error);
    return false;
  }
}

/**
 * Get a value from Redis hash
 * @param hash The hash name
 * @param field The field in the hash
 * @returns The value associated with the field, or null if not found
 */
export async function getHashField<T>(
  hash: string,
  field: string
): Promise<T | null> {
  try {
    return await redis.hget(hash, field);
  } catch (error) {
    console.error(`Error getting Redis hash field ${hash}:${field}:`, error);
    return null;
  }
}

/**
 * Get all fields and values from a Redis hash
 * @param hash The hash name
 * @returns An object containing all fields and values, or null if not found
 */
export async function getHashAll<T>(hash: string): Promise<Record<string, T> | null> {
  try {
    return await redis.hgetall(hash);
  } catch (error) {
    console.error(`Error getting all Redis hash fields for ${hash}:`, error);
    return null;
  }
}

export default redis;
