'use client'

/**
 * Interface for the client information stored in session storage
 */
export interface ClientInfo {
  client_id: string;
  username: string;
  subscription_tier: string | null; // Assuming these can be null
  sector: string | null;
  lang: string | null;
}

/**
 * Retrieves the client info object from session storage
 * @returns The client info object, or null if not found or invalid
 */
export function getClientInfo(): ClientInfo | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const clientInfoString = sessionStorage.getItem('clientInfo');
  if (!clientInfoString) {
    return null;
  }

  try {
    const clientInfo = JSON.parse(clientInfoString);
    // Basic validation to ensure it has client_id
    if (clientInfo && typeof clientInfo.client_id === 'string') {
      return clientInfo as ClientInfo;
    }
    return null;
  } catch (error) {
    console.error("Error parsing client info from session storage:", error);
    // Clear potentially corrupted data
    sessionStorage.removeItem('clientInfo');
    return null;
  }
}

/**
 * Retrieves just the client_id from the client info in session storage
 * @returns The client_id string, or null if not found
 */
export function getClientId(): string | null {
  const clientInfo = getClientInfo();
  return clientInfo?.client_id || null;
}

/**
 * Sets the client info object in session storage
 * @param info The client info object to store
 */
export function setClientInfo(info: ClientInfo): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    const clientInfoString = JSON.stringify(info);
    sessionStorage.setItem('clientInfo', clientInfoString);
  } catch (error) {
    console.error("Error stringifying client info for session storage:", error);
  }
}

/**
 * Clears the client info from session storage
 */
export function clearClientInfo(): void {
  if (typeof window === 'undefined') {
    return;
  }

  sessionStorage.removeItem('clientInfo');
}