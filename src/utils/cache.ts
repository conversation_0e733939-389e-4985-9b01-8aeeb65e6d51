'use client'

import { getRedisValue, setRedisValue, deleteRedisValue } from '@/utils/redis/clientApi'

export interface CacheConfig {
  ttl: number // Time to live in seconds
  prefix: string
}

export interface PhotoData {
  id: string
  photo_id: string
  photo_url: string
  photo_file_path: string
}

/**
 * Smart caching utility for client-side data
 */
export class SmartCache {
  private static instance: SmartCache
  private memoryCache: Map<string, { data: any; expires: number }> = new Map()

  static getInstance(): SmartCache {
    if (!SmartCache.instance) {
      SmartCache.instance = new SmartCache()
    }
    return SmartCache.instance
  }

  /**
   * Get data from cache (memory first, then Redis)
   */
  async get<T>(key: string, config?: CacheConfig): Promise<T | null> {
    const fullKey = config?.prefix ? `${config.prefix}:${key}` : key

    // Check memory cache first
    const memoryItem = this.memoryCache.get(fullKey)
    if (memoryItem && memoryItem.expires > Date.now()) {
      return memoryItem.data as T
    }

    // Check Redis cache
    try {
      const redisData = await getRedisValue<T>(fullKey)
      if (redisData) {
        // Store in memory cache for faster access
        this.memoryCache.set(fullKey, {
          data: redisData,
          expires: Date.now() + (config?.ttl || 300) * 1000
        })
        return redisData
      }
    } catch (error) {
      console.error('Error getting from Redis cache:', error)
    }

    return null
  }

  /**
   * Set data in cache (both memory and Redis)
   */
  async set<T>(key: string, data: T, config?: CacheConfig): Promise<boolean> {
    const fullKey = config?.prefix ? `${config.prefix}:${key}` : key
    const ttl = config?.ttl || 300

    // Set in memory cache
    this.memoryCache.set(fullKey, {
      data,
      expires: Date.now() + ttl * 1000
    })

    // Set in Redis cache
    try {
      return await setRedisValue(fullKey, data, ttl)
    } catch (error) {
      console.error('Error setting Redis cache:', error)
      return false
    }
  }

  /**
   * Invalidate cache entry
   */
  async invalidate(key: string, config?: CacheConfig): Promise<boolean> {
    const fullKey = config?.prefix ? `${config.prefix}:${key}` : key

    // Remove from memory cache
    this.memoryCache.delete(fullKey)

    // Remove from Redis cache
    try {
      return await deleteRedisValue(fullKey)
    } catch (error) {
      console.error('Error invalidating Redis cache:', error)
      return false
    }
  }

  /**
   * Clear all memory cache
   */
  clearMemoryCache(): void {
    this.memoryCache.clear()
  }

  /**
   * Clean expired entries from memory cache
   */
  cleanExpired(): void {
    const now = Date.now()
    // Fix for TypeScript iteration issue
    this.memoryCache.forEach((item, key) => {
      if (item.expires <= now) {
        this.memoryCache.delete(key)
      }
    })
  }
}

/**
 * Photo cache management
 */
export class PhotoCache {
  private static cache = SmartCache.getInstance()
  private static config: CacheConfig = { ttl: 1800, prefix: 'photos' } // 30 minutes - photos rarely change

  static async getAllPhotos(clientId: string): Promise<PhotoData[] | null> {
    return this.cache.get<PhotoData[]>(`all:${clientId}`, this.config)
  }

  static async setAllPhotos(clientId: string, photos: PhotoData[]): Promise<boolean> {
    return this.cache.set(`all:${clientId}`, photos, this.config)
  }

  static async searchPhotos(clientId: string, query: string): Promise<PhotoData[] | null> {
    // Get all photos from cache (memory or Redis)
    const allPhotos = await this.getAllPhotos(clientId)

    if (allPhotos) {
      // Filter locally - instant and free!
      return allPhotos.filter(photo =>
        photo.photo_id.toLowerCase().includes(query.toLowerCase())
      )
    }

    // Only return null if no cache at all (will trigger database fetch)
    return null
  }

  static async invalidatePhotos(clientId: string): Promise<void> {
    // Invalidate all photos cache for this client
    await this.cache.invalidate(`all:${clientId}`, this.config)

    // Note: We can't easily invalidate all search results, but they have shorter TTL
    // In a production system, you might want to track search keys for invalidation
  }

  // Get the exact cache key for manual invalidation
  static getCacheKey(clientId: string): string {
    return `photos:all:${clientId}`
  }
}

/**
 * Client info cache management
 */
export class ClientInfoCache {
  private static cache = SmartCache.getInstance()
  private static config: CacheConfig = { ttl: 1800, prefix: 'client' } // 30 minutes - client info rarely changes

  static async getClientInfo(clientId: string): Promise<any | null> {
    return this.cache.get(`info:${clientId}`, this.config)
  }

  static async setClientInfo(clientId: string, info: any): Promise<boolean> {
    return this.cache.set(`info:${clientId}`, info, this.config)
  }

  static async invalidateClientInfo(clientId: string): Promise<void> {
    await this.cache.invalidate(`info:${clientId}`, this.config)
  }

  // Get the exact cache key for manual invalidation
  static getCacheKey(clientId: string): string {
    return `client:info:${clientId}`
  }
}

/**
 * FAQ cache management
 */
export class FaqCache {
  private static cache = SmartCache.getInstance()
  private static config: CacheConfig = { ttl: 1200, prefix: 'faqs' } // 20 minutes - FAQs change occasionally

  static async getFaqs(clientId: string): Promise<any[] | null> {
    return this.cache.get<any[]>(`list:${clientId}`, this.config)
  }

  static async setFaqs(clientId: string, faqs: any[]): Promise<boolean> {
    return this.cache.set(`list:${clientId}`, faqs, this.config)
  }

  static async getFaqCount(clientId: string): Promise<number | null> {
    return this.cache.get<number>(`count:${clientId}`, this.config)
  }

  static async setFaqCount(clientId: string, count: number): Promise<boolean> {
    return this.cache.set(`count:${clientId}`, count, this.config)
  }

  static async invalidateFaqs(clientId: string): Promise<void> {
    await this.cache.invalidate(`list:${clientId}`, this.config)
    await this.cache.invalidate(`count:${clientId}`, this.config)
  }

  // Get the exact cache keys for manual invalidation
  static getListCacheKey(clientId: string): string {
    return `faqs:list:${clientId}`
  }

  static getCountCacheKey(clientId: string): string {
    return `faqs:count:${clientId}`
  }
}

/**
 * Dashboard cache management
 */
export class DashboardCache {
  private static cache = SmartCache.getInstance()
  private static config: CacheConfig = { ttl: 1800, prefix: 'dashboard' } // 30 minutes - dashboard data rarely changes

  static async getDashboardData(clientId: string): Promise<any | null> {
    return this.cache.get(`data:${clientId}`, this.config)
  }

  static async setDashboardData(clientId: string, data: any): Promise<boolean> {
    return this.cache.set(`data:${clientId}`, data, this.config)
  }

  static async invalidateDashboardData(clientId: string): Promise<void> {
    await this.cache.invalidate(`data:${clientId}`, this.config)
  }

  // Get the exact cache key for manual invalidation
  static getCacheKey(clientId: string): string {
    return `dashboard:data:${clientId}`
  }
}

// Initialize cache cleanup interval
if (typeof window !== 'undefined') {
  setInterval(() => {
    SmartCache.getInstance().cleanExpired()
  }, 60000) // Clean every minute
}
