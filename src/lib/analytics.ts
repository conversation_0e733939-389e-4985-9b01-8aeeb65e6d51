// Web Analytics Configuration
// This file contains utility functions for analytics tracking

// Custom event tracking functions
export const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  if (typeof window !== 'undefined') {
    // Analytics events are handled automatically by the web analytics service
    // This is a placeholder for future custom event tracking if needed
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Event:', eventName, properties)
    }
  }
}

// Predefined events for ChhlatBot
export const analytics = {
  // User registration events
  trackRegistration: (step: string, sector?: string) => {
    trackEvent('registration', { step, sector })
  },

  // Authentication events
  trackLogin: (method: 'email' | 'social') => {
    trackEvent('login', { method })
  },

  trackLogout: () => {
    trackEvent('logout')
  },

  // Dashboard events
  trackDashboardView: (page: string) => {
    trackEvent('dashboard_view', { page })
  },

  // Social media integration events
  trackSocialMediaConnect: (platform: 'facebook' | 'instagram' | 'telegram') => {
    trackEvent('social_media_connect', { platform })
  },

  trackSocialMediaDisconnect: (platform: 'facebook' | 'instagram' | 'telegram') => {
    trackEvent('social_media_disconnect', { platform })
  },

  // Knowledge base events
  trackKnowledgeBaseUpdate: (action: 'create' | 'update' | 'delete', type: 'text' | 'audio' | 'image') => {
    trackEvent('knowledge_base_update', { action, type })
  },

  // Settings events
  trackSettingsChange: (setting: 'password' | 'language' | 'notifications') => {
    trackEvent('settings_change', { setting })
  },

  // AI response events
  trackAIResponse: (platform: string, responseType: 'text' | 'audio' | 'image') => {
    trackEvent('ai_response', { platform, responseType })
  },

  // Error tracking
  trackError: (errorType: string, errorMessage?: string) => {
    trackEvent('error', { errorType, errorMessage })
  },

  // Performance tracking
  trackPerformance: (action: string, duration: number) => {
    trackEvent('performance', { action, duration })
  }
}

// Environment check
export const isAnalyticsEnabled = () => {
  return process.env.NODE_ENV === 'production'
}
