import { Inter, Plus_Jakarta_Sans, <PERSON>g<PERSON>, Noto_Sans_Khmer } from 'next/font/google'

// Define our fonts
export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const plusJakartaSans = Plus_Jakarta_Sans({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-plus-jakarta-sans',
})

// Add Khmer fonts
export const dangrek = Dangrek({
  weight: '400',
  subsets: ['khmer'],
  display: 'swap',
  variable: '--font-khmer-title',
})

export const notoSansKhmer = Noto_Sans_Khmer({
  subsets: ['khmer'],
  display: 'swap',
  variable: '--font-khmer-body',
})

// Export all font variables as a combined string for easy use in className
export const fontVariables = `${inter.variable} ${plusJakartaSans.variable} ${dangrek.variable} ${notoSansKhmer.variable}`
