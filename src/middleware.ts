import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { updateSession } from './utils/supabase/middleware'

export async function middleware(request: NextRequest) {
  try {
    // Call updateSession to refresh the user's session if needed
    const response = await updateSession(request)
    return response
  } catch (error) {
    console.error('Middleware error:', error)
    // In case of an error, proceed without modifying the request
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    })
  }
}

// Specify which paths this middleware should run on
export const config = {
  matcher: [
    // Match all paths except static assets, API routes, and specific files
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|css|js|woff|woff2|ttf|eot)$|api/|not-found).*)',
  ],
}