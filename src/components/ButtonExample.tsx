'use client'

import React from 'react'
import GlassButton from './ui/GlassButton'

export default function ButtonExample() {
  return (
    <div className="flex flex-col md:flex-row gap-4 justify-center items-center p-8">
      <div className="flex flex-col gap-4 items-center">
        <h3 className="text-white font-title text-lg mb-2">With Shadow</h3>
        <GlassButton variant="filled">
          Filled Button
        </GlassButton>
        <GlassButton variant="outline">
          Outline Button
        </GlassButton>
      </div>
      
      <div className="flex flex-col gap-4 items-center">
        <h3 className="text-white font-title text-lg mb-2">Without Shadow</h3>
        <GlassButton variant="filled-flat">
          Filled Flat Button
        </GlassButton>
        <GlassButton variant="outline-flat">
          Outline Flat Button
        </GlassButton>
      </div>
    </div>
  )
}
