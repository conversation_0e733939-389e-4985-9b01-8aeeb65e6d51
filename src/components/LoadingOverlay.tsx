'use client'

import { useEffect, useState } from 'react'
import { useLanguage } from '@/context/LanguageContext'

interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
}

export default function LoadingOverlay({ isVisible, message }: LoadingOverlayProps) {
  const { t } = useLanguage()
  const [showDelayed, setShowDelayed] = useState(false)

  // For language switching, we want the overlay to appear immediately
  useEffect(() => {
    if (isVisible) {
      // Show immediately for better user experience
      setShowDelayed(true)
    } else {
      setShowDelayed(false)
    }
  }, [isVisible])

  if (!isVisible) return null

  return (
    <div
      className="fixed inset-0 bg-deep-blue/80 backdrop-blur-sm z-[9999] flex flex-col items-center justify-center"
      style={{
        opacity: showDelayed ? 1 : 0,
        // Fast fade-in (0.1s), slower fade-out (0.3s) for better UX
        transition: showDelayed ? 'opacity 0.1s ease' : 'opacity 0.3s ease',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        height: '100vh',
        width: '100vw',
        touchAction: 'none',
        // Ensure it's visible on all mobile browsers
        zIndex: 9999
      }}
    >
      <div className="text-center px-4">
        <div className="w-16 h-16 border-4 border-jade-purple border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
        <p className="text-white font-title text-xl mb-2">{message || t('changing_language')}</p>
        <p className="text-white/70 text-sm mb-4">{t('please_wait')}</p>
        <div className="w-full max-w-xs h-1 bg-white/20 rounded-full overflow-hidden mt-4">
          <div
            className="h-full bg-gradient-to-r from-jade-purple to-purple-500"
            style={{
              width: '0%',
              animation: 'progressAnim 2s forwards'
            }}
          ></div>
        </div>
      </div>
      <style jsx>{`
        @keyframes progressAnim {
          from { width: 0%; }
          to { width: 100%; }
        }
      `}</style>
    </div>
  )
}
