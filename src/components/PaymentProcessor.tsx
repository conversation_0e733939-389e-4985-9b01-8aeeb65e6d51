'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useLanguage } from '@/context/LanguageContext'
import Image from 'next/image'
import { pricingPlans } from '@/lib/pricing'

interface PaymentProcessorProps {
  planType: string
  billingCycle: number
  onCancel: () => void
}

export default function PaymentProcessor({ planType, billingCycle, onCancel }: PaymentProcessorProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [paymentData, setPaymentData] = useState<any>(null)
  const [qrCode, setQrCode] = useState<string | null>(null)
  const [deepLink, setDeepLink] = useState<string | null>(null)
  const { t } = useLanguage()

  useEffect(() => {
    const initiatePayment = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Call our payment processing API
        const response = await fetch('/api/payment/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            planType,
            billingCycle,
          }),
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Failed to process payment')
        }

        if (!data.success || !data.paymentData) {
          throw new Error('Invalid response from payment API')
        }

        // Log the payment data
        console.log('Payment data received:', data.paymentData);

        // Store the payment data
        setPaymentData(data.paymentData);

        // Check if we have QR code and deeplink
        if (data.paymentData.status && data.paymentData.status.code === '00') {
          // Success response from PayWay

          // Check for qrImage (base64 encoded image)
          if (data.paymentData.qrImage) {
            console.log('QR image received (base64)');
            setQrCode(data.paymentData.qrImage);
          }
          // Check for checkout_qr_url (URL to hosted QR image)
          else if (data.paymentData.checkout_qr_url) {
            console.log('QR URL received');
            setQrCode(data.paymentData.checkout_qr_url);
          }

          if (data.paymentData.abapay_deeplink) {
            console.log('ABA deeplink received');
            setDeepLink(data.paymentData.abapay_deeplink);
          }
        }

      } catch (error: any) {
        console.error('Payment initiation error:', error)
        setError(error.message || 'Failed to initiate payment')
      } finally {
        setIsLoading(false)
      }
    }

    initiatePayment()
  }, [planType, billingCycle])

  if (error) {
    return (
      <div className="p-6 rounded-xl bg-red-500/20 border border-red-500/30">
        <h3 className="text-xl font-semibold mb-2 text-red-400">Payment Error</h3>
        <p className="mb-4">{error}</p>
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-md transition-colors"
        >
          {t('try_again')}
        </button>
      </div>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto p-6 rounded-xl relative overflow-hidden" style={{
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      border: '1px solid rgba(255, 255, 255, 0.3)',
      boxShadow: '0 0 20px rgba(134, 107, 255, 0.3), inset 0 0 20px rgba(134, 107, 255, 0.2)'
    }}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold mb-2 font-title">Payment</h2>
      </div>

      {/* Close button in top right corner */}
      <button
        className="absolute top-2 right-2 p-2 rounded-full bg-white/5 hover:bg-white/20 transition-colors"
        onClick={onCancel}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>

      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-8">
          <div className="w-16 h-16 border-t-2 border-b-2 border-jade-purple rounded-full animate-spin mb-4"></div>
          <p className="text-zinc-400">Processing...</p>
        </div>
      ) : qrCode ? (
        // Display QR code and deeplink
        <div className="flex flex-col items-center">
          {/* <h3 className="text-xl font-semibold mb-2">Payment</h3> */}

          {/* Price calculation */}
          <div className="mb-4">
            <p className="text-xl font-bold text-white font-body">
              {(() => {
                // Find the plan in pricingPlans
                const plan = pricingPlans.find(p => p.nameKey.replace('plan_', '').toLowerCase() === planType);

                if (plan) {
                  // Get the monthly price
                  const monthlyPrice = plan.prices[billingCycle];
                  // For 3-month billing cycle, multiply by 3
                  const totalPrice = billingCycle === 0 ? monthlyPrice : (monthlyPrice * 3);
                  return `$${totalPrice.toFixed(2)}`;
                }

                // Fallback if plan not found
                return billingCycle === 0 ?
                  (planType === 'intern' ? '$7.49' : planType === 'assistant' ? '$12.49' : '$14.99') :
                  (planType === 'intern' ? '$17.97' : planType === 'assistant' ? '$29.97' : '$35.97');
              })()}
            </p>
          </div>

          {/* QR Code Image */}
          <div className="bg-white p-4 rounded-lg mb-6">
            <img
              src={qrCode}
              alt="Payment QR Code"
              className="w-64 h-64 object-contain"
            />
          </div>

          {/* Deeplink Button */}
          {deepLink && (
            <a
              href={deepLink}
              className="w-full py-3 px-4 bg-[#245A78] text-white rounded-lg flex items-center justify-center mb-4"
              target="_blank"
              rel="noopener noreferrer"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              Open in ABA Mobile
            </a>
          )}

          <p className="text-sm text-zinc-400 text-center mb-6">
            Scan with ABA Mobile, or other Mobile Banking App supporting KHQR
          </p>

          <div className="flex justify-center w-full">
            <motion.button
              className="px-6 py-2 bg-jade-purple hover:bg-jade-purple-dark rounded-md transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                // Refresh the page to check payment status
                window.location.href = '/dashboard/plans';
              }}
            >
              I've Paid
            </motion.button>
          </div>
        </div>
      ) : paymentData ? (
        // Fallback if no QR code is available
        <div>
          <p className="mb-4 text-center">
            No QR code was received from the payment gateway. Please try again or contact support.
          </p>

          <div className="flex justify-center">
            <motion.button
              className="px-4 py-2 bg-jade-purple hover:bg-jade-purple-dark rounded-md transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.location.reload()}
            >
              Try Again
            </motion.button>
          </div>
        </div>
      ) : null}
    </div>
  )
}
