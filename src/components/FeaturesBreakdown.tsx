'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'

// Define types for our data structures
type Message = {
  sender: 'customer' | 'bot';
  message: string;
}

type Platform = 'facebook' | 'instagram' | 'tiktok';

type PlatformStyle = {
  bg: string;
  header: string;
  msgBg: string;
  bubble: string;
}

type Feature = {
  title: string;
  description: string;
  icon: string;
  platform: Platform;
  platformName: string;
  conversation: Message[];
}

export default function FeaturesBreakdown() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const features: Feature[] = [
    {
      title: 'Auto-Reply System',
      description: 'Our AI automatically responds to common questions across all your social media platforms',
      icon: '🤖',
      platform: 'facebook',
      platformName: 'Facebook Messenger',
      conversation: [
        { sender: 'customer', message: 'Hi! What are your store hours today?' },
        { sender: 'bot', message: 'Hello! Our store is open today from 9am to 8pm. Is there anything specific you\'re looking for? I\'d be happy to help!' },
        { sender: 'customer', message: 'Thanks! Do you have the new Nike Air Max in stock?' },
        { sender: 'bot', message: 'Yes, we do have the Nike Air Max in stock in most sizes. Would you like me to check availability for your size?' }
      ]
    },
    {
      title: 'Multi-Account Management',
      description: 'Connect all your business accounts from different platforms to a single dashboard',
      icon: '🔄',
      platform: 'instagram',
      platformName: 'Instagram DM',
      conversation: [
        { sender: 'customer', message: 'Can I get a refund for my order #4578? The size doesn\'t fit.' },
        { sender: 'bot', message: 'I\'d be happy to help with your refund request for order #4578. Please confirm your email address and I\'ll get the process started right away.' },
        { sender: 'customer', message: 'My <NAME_EMAIL>' },
        { sender: 'bot', message: 'Thank you! I\'ve initiated the refund process for order #4578. You should receive a confirmation email shortly, and the funds will appear in your account within 3-5 business days.' }
      ]
    },
    {
      title: 'Business Hour Settings',
      description: 'Configure when to use AI replies and when to notify you for human intervention',
      icon: '⏰',
      platform: 'tiktok',
      platformName: 'TikTok Messages',
      conversation: [
        { sender: 'customer', message: 'Is the discount code from your latest video still valid?' },
        { sender: 'bot', message: 'Yes! The discount code "TIKTOK25" from our latest video is still valid. You can use it to get 25% off your purchase until the end of this week!' },
        { sender: 'customer', message: 'Awesome! Can I use it on sale items too?' },
        { sender: 'bot', message: 'Unfortunately, the discount code cannot be applied to items already on sale. But it works for all regular-priced items! Is there something specific you were looking to purchase?' }
      ]
    }
  ]

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  // Platform-specific styling
  const platformStyles: Record<Platform, PlatformStyle> = {
    facebook: {
      bg: 'bg-white',
      header: 'bg-[#0084FF]',
      msgBg: 'bg-[#0084FF]',
      bubble: 'rounded-2xl',
    },
    instagram: {
      bg: 'bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#FCAF45]',
      header: 'bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#FCAF45]',
      msgBg: 'bg-gradient-to-r from-[#833AB4] to-[#FD1D1D]',
      bubble: 'rounded-2xl',
    },
    tiktok: {
      bg: 'bg-deep-blue',
      header: 'bg-deep-blue border-b border-zinc-800',
      msgBg: 'bg-[#FE2C55]',
      bubble: 'rounded-xl',
    }
  }

  return (
    <section className="section bg-deep-blue px-4 sm:px-4" ref={ref}>
      <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: 20 }}
        animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 font-title">
          <span className="gradient-text">Powerful</span> Social Media Automation
        </h2>
        <p className="max-w-2xl mx-auto text-base sm:text-lg font-body">
          Handle messages from all platforms with one powerful system
        </p>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-12"
        variants={container}
        initial="hidden"
        animate={inView ? "show" : "hidden"}
      >
        {features.slice(0, 2).map((feature, index) => (
          <motion.div
            key={index}
            className="flex flex-col gap-8"
            variants={item}
          >
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <span className="w-10 h-10 bg-jade-purple rounded-lg flex items-center justify-center text-white font-body text-lg">
                  {feature.icon}
                </span>
                <h3 className="text-xl font-bold font-title">{feature.title}</h3>
              </div>
              <p className="text-base sm:text-lg font-body text-gray-300">{feature.description}</p>
            </div>

            <div className="w-full">
              <div className="overflow-hidden rounded-lg border border-zinc-800 shadow-lg">
                {/* Platform header */}
                <div className={`${platformStyles[feature.platform].header} px-4 py-3 flex items-center`}>
                  <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 text-black font-bold text-sm">
                    {feature.platformName.charAt(0)}
                  </div>
                  <div className="flex-1">
                    <div className="text-white font-title font-bold">{feature.platformName}</div>
                    <div className="text-white text-xs opacity-80 font-body">Your Business</div>
                  </div>
                </div>

                {/* Chat area */}
                <div className="bg-[#111] h-[250px] p-4 overflow-y-auto">
                  {feature.conversation.map((msg, idx) => (
                    <div
                      key={idx}
                      className={`mb-3 flex ${msg.sender === 'customer' ? 'justify-start' : 'justify-end'}`}
                    >
                      {msg.sender === 'customer' && (
                        <div className="w-8 h-8 rounded-full bg-zinc-700 mr-2 flex-shrink-0 flex items-center justify-center">
                          <span className="text-white text-xs">C</span>
                        </div>
                      )}

                      <div className="max-w-[80%]">
                        <div className={`${platformStyles[feature.platform].bubble} px-4 py-2 ${
                          msg.sender === 'customer'
                            ? 'bg-zinc-800 text-white'
                            : `${platformStyles[feature.platform].msgBg} text-white`
                        }`}>
                          <p className="font-body text-sm">{msg.message}</p>
                        </div>
                      </div>

                      {msg.sender === 'bot' && (
                        <div className="w-8 h-8 rounded-full bg-jade-purple ml-2 flex-shrink-0 flex items-center justify-center">
                          <span className="text-white text-xs">B</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Input area */}
                <div className="bg-deep-blue px-4 py-3 border-t border-zinc-800 flex items-center">
                  <div className="flex-1 bg-zinc-800 rounded-full px-4 py-2 text-white font-body text-sm">
                    Message...
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Business Hour Settings as its own row */}
      <motion.div
        className="mt-16"
        variants={container}
        initial="hidden"
        animate={inView ? "show" : "hidden"}
      >
        {features.slice(2, 3).map((feature, index) => (
          <motion.div
            key={index}
            className="flex flex-col gap-8"
            variants={item}
          >
            <div className="text-center space-y-4 max-w-2xl mx-auto">
              <div className="flex items-center gap-3 justify-center">
                <span className="w-10 h-10 bg-jade-purple rounded-lg flex items-center justify-center text-white font-body text-lg">
                  {feature.icon}
                </span>
                <h3 className="text-xl font-bold font-title">{feature.title}</h3>
              </div>
              <p className="text-base sm:text-lg font-body text-gray-300">{feature.description}</p>
            </div>

            <div className="w-full max-w-2xl mx-auto">
              <div className="overflow-hidden rounded-lg border border-zinc-800 shadow-lg">
                {/* Platform header */}
                <div className={`${platformStyles[feature.platform].header} px-4 py-3 flex items-center`}>
                  <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 text-black font-bold text-sm">
                    {feature.platformName.charAt(0)}
                  </div>
                  <div className="flex-1">
                    <div className="text-white font-title font-bold">{feature.platformName}</div>
                    <div className="text-white text-xs opacity-80 font-body">Your Business</div>
                  </div>
                </div>

                {/* Chat area */}
                <div className="bg-[#111] h-[250px] p-4 overflow-y-auto">
                  {feature.conversation.map((msg, idx) => (
                    <div
                      key={idx}
                      className={`mb-3 flex ${msg.sender === 'customer' ? 'justify-start' : 'justify-end'}`}
                    >
                      {msg.sender === 'customer' && (
                        <div className="w-8 h-8 rounded-full bg-zinc-700 mr-2 flex-shrink-0 flex items-center justify-center">
                          <span className="text-white text-xs">C</span>
                        </div>
                      )}

                      <div className="max-w-[80%]">
                        <div className={`${platformStyles[feature.platform].bubble} px-4 py-2 ${
                          msg.sender === 'customer'
                            ? 'bg-zinc-800 text-white'
                            : `${platformStyles[feature.platform].msgBg} text-white`
                        }`}>
                          <p className="font-body text-sm">{msg.message}</p>
                        </div>
                      </div>

                      {msg.sender === 'bot' && (
                        <div className="w-8 h-8 rounded-full bg-jade-purple ml-2 flex-shrink-0 flex items-center justify-center">
                          <span className="text-white text-xs">B</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Input area */}
                <div className="bg-deep-blue px-4 py-3 border-t border-zinc-800 flex items-center">
                  <div className="flex-1 bg-zinc-800 rounded-full px-4 py-2 text-white font-body text-sm">
                    Message...
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </section>
  )
}