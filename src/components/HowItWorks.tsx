'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { useLanguage } from '@/context/LanguageContext'

export default function HowItWorks() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })
  const { t } = useLanguage()

  const steps = [
    {
      number: '01',
      titleKey: 'step1_title',
      descriptionKey: 'step1_desc',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-jade-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M12 5l7 7-7 7" />
        </svg>
      )
    },
    {
      number: '02',
      titleKey: 'step2_title',
      descriptionKey: 'step2_desc',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-jade-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      )
    },
    {
      number: '03',
      titleKey: 'step3_title',
      descriptionKey: 'step3_desc',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-jade-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
        </svg>
      )
    }
  ]



  return (
    <section className="py-24 relative overflow-hidden" ref={ref}>
      {/* Grid pattern */}
      {/* <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-12 gap-4 h-full">
          {Array.from({ length: 24 }).map((_, i) => (
            <div key={i} className="h-full w-px bg-white opacity-10" style={{ left: `${(i / 24) * 100}%` }}></div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="h-px w-full bg-white opacity-10" style={{ top: `${(i / 12) * 100}%` }}></div>
          ))}
        </div>
      </div> */}

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 font-title"
            dangerouslySetInnerHTML={{ __html: t('how_title') }}
            initial={{ opacity: 0, y: 30 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
          />
          <motion.p
            className="max-w-2xl mx-auto text-base sm:text-lg text-[#CCCCCC] font-body"
            initial={{ opacity: 0, y: 30 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          >
            {t('how_subtitle')}
          </motion.p>
        </div>

        <div className="max-w-4xl mx-auto relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-gradient-to-b from-[#866bff] to-[#CCCCCC] opacity-30 hidden md:block"></div>

          <div className="space-y-16 md:space-y-14">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                className="relative"
                initial={{ opacity: 0, y: 30 }}
                animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{
                  duration: 0.6,
                  delay: 0.3 + index * 0.2,
                  ease: "easeOut"
                }}
              >
                <div className={`flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} items-center`}>
                  {/* Timeline dot */}
                  <motion.div
                    className="absolute left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full bg-gradient-to-br from-jade-purple/80 to-jade-purple/60 border-2 border-jade-purple/40 hidden md:flex items-center justify-center z-10 backdrop-blur-sm"
                    style={{
                      boxShadow: '0 8px 32px rgba(134, 107, 255, 0.3), 0 0 0 1px rgba(134, 107, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                    }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                    transition={{
                      duration: 0.6,
                      delay: 0.4 + index * 0.2,
                      ease: "easeOut"
                    }}
                  >
                    <span className="text-white font-bold">{step.number}</span>
                  </motion.div>

                  {/* Content card */}
                  <div
                    className={`relative w-full md:w-5/12 bg-gradient-to-br from-white/[0.08] to-white/[0.02] backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:border-white/50 transition-colors duration-300 overflow-hidden ${index % 2 === 0 ? 'md:mr-12' : 'md:ml-12'}`}
                    style={{
                      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                    }}
                  >

                    <div className="relative z-10">
                      <div className="flex items-center mb-4">
                        <div className="mr-4 w-12 h-12 bg-jade-purple/20 rounded-xl flex items-center justify-center border border-jade-purple/30" style={{
                          boxShadow: '0 4px 16px rgba(134, 107, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                        }}>
                          {step.icon}
                        </div>
                        <div>
                          <span className="text-sm text-jade-purple font-medium">{t('step')} {step.number}</span>
                          <h3 className="text-xl font-bold text-white font-title">{t(step.titleKey)}</h3>
                        </div>
                      </div>
                      <p className="text-zinc-300 font-body leading-relaxed">
                        {t(step.descriptionKey)}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.9, ease: "easeOut" }}
        >
        </motion.div>
      </div>
    </section>
  )
}