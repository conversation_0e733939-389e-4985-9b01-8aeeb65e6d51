'use client'

import { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'

interface Particle {
  x: number
  y: number
  size: number
  speedX: number
  speedY: number
  opacity: number
}

export default function CombinedBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isLowPowerMode, setIsLowPowerMode] = useState(false)

  // We're not using low power mode anymore, but keeping the state for future use
  useEffect(() => {
    // Always show all effects regardless of device
    setIsLowPowerMode(false)
  }, [])

  // Particle background effect
  useEffect(() => {
    // Skip canvas rendering in low power mode
    if (isLowPowerMode) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas to full screen and reinitialize particles on resize
    const initParticles = (width: number, height: number) => {
      // More particles for desktop, still reasonable for performance
      const count = Math.min(width / 8, 120) // More particles
      const newParticles: Particle[] = []

      for (let i = 0; i < count; i++) {
        newParticles.push({
          x: Math.random() * width,
          y: Math.random() * height,
          size: Math.random() * 1 + 0.6, // Larger particles
          speedX: (Math.random() - 0.5) * 0.2, // Slightly faster movement
          speedY: (Math.random() - 0.5) * 0.2,
          opacity: Math.random() * 0.5 + 0.25 // Higher opacity
        })
      }

      return newParticles
    }

    const handleResize = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight

      // Reinitialize particles when window is resized
      particles.length = 0
      const newParticles = initParticles(canvas.width, canvas.height)
      particles.push(...newParticles)
    }

    // Create particles array
    const particles: Particle[] = []

    // Initial resize will populate the particles
    window.addEventListener('resize', handleResize)
    handleResize()

    // Animation loop with frame skipping for better performance
    let frameCount = 0
    const animate = () => {
      frameCount++

      // Only render every other frame for better performance
      if (frameCount % 2 === 0) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // Update and draw particles
        particles.forEach(particle => {
          // Move particle
          particle.x += particle.speedX
          particle.y += particle.speedY

          // Wrap around edges
          if (particle.x < 0) particle.x = canvas.width
          if (particle.x > canvas.width) particle.x = 0
          if (particle.y < 0) particle.y = canvas.height
          if (particle.y > canvas.height) particle.y = 0

          // Draw particle with glow effect for better visibility
          // Outer glow
          ctx.beginPath()
          ctx.arc(particle.x, particle.y, particle.size + 1.5, 0, Math.PI * 2)
          ctx.fillStyle = `rgba(134, 107, 255, ${particle.opacity * 0.3})`
          ctx.fill()

          // Inner particle
          ctx.beginPath()
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
          ctx.fillStyle = `rgba(134, 107, 255, ${particle.opacity})`
          ctx.fill()
        })

        // No connections between particles - just flowing dots
      }

      requestAnimationFrame(animate)
    }

    const animationId = requestAnimationFrame(animate)

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize)
      cancelAnimationFrame(animationId)
    }
  }, [isLowPowerMode])

  return (
    <>
      {/* Gradient Waves - more colorful and always visible */}
      <div className="fixed inset-0 z-0 overflow-hidden opacity-80">
        {/* First wave (top layer) - Darker color */}
        <motion.div
          className="absolute w-[230%] h-[35vh] bottom-[-5%] left-[-65%] bg-gradient-to-r from-jade-purple/15 via-jade-purple/30 to-jade-purple/15"
          style={{
            borderRadius: '45% 55% 45% 55% / 100% 100% 0% 0%',
            transform: 'scaleX(1.4) scaleY(0.6)'
          }}
          animate={{
            y: [0, -10, 0],
            x: [0, 8, 0],
            rotate: [0, 0.8, 0]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Second wave (middle layer) */}
        <motion.div
          className="absolute w-[240%] h-[45vh] bottom-[-15%] left-[-70%] bg-gradient-to-r from-jade-purple/10 via-jade-purple/20 to-jade-purple/10"
          style={{
            borderRadius: '60% 40% 60% 40% / 100% 100% 0% 0%',
            transform: 'scaleX(1.3) scaleY(0.7)'
          }}
          animate={{
            y: [0, -15, 0],
            x: [0, -12, 0],
            rotate: [0, -1.5, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Third wave (bottom layer) - Brightest color */}
        <motion.div
          className="absolute w-[220%] h-[40vh] bottom-[-10%] left-[-60%] bg-gradient-to-r from-deep-blue/5 via-deep-blue/10 to-deep-blue/5"
          style={{
            borderRadius: '50% 50% 50% 50% / 100% 100% 0% 0%',
            transform: 'scaleX(1.2) scaleY(0.8)'
          }}
          animate={{
            y: [0, -12, 0],
            x: [0, 10, 0],
            rotate: [0, 1.2, 0]
          }}
          transition={{
            duration: 22,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Particle Canvas - always render now */}
      <canvas
        ref={canvasRef}
        className="fixed top-0 left-0 w-full h-full z-0 opacity-80"
        style={{
          pointerEvents: 'none',
          filter: 'blur(0.5px) brightness(1.05)', // Subtle blur and brightness for glow effect
        }}
      />
    </>
  )
}
