'use client'

import { useLanguage } from '@/context/LanguageContext'
import { useLoading } from '@/context/LoadingContext'
import { useEffect, useState } from 'react'

export default function LanguageSwitcher() {
  const { language, setLanguage, t } = useLanguage()
  const { showLoading, hideLoading } = useLoading()
  const [isChangingLanguage, setIsChangingLanguage] = useState(false)

  // Handle the actual page reload
  useEffect(() => {
    if (isChangingLanguage) {
      // Use a longer delay to ensure the loading animation is visible
      // This gives a better user experience by showing the loading state
      const reloadTimeout = setTimeout(() => {
        try {
          // Force a hard reload to ensure the page is completely refreshed
          window.location.href = window.location.href.split('#')[0]
        } catch (error) {
          console.error('Error during language reload:', error)
          // Fallback to standard reload if the above fails
          window.location.reload()
        }
      }, 500) // Longer timeout to ensure loading animation is visible

      return () => clearTimeout(reloadTimeout)
    }
  }, [isChangingLanguage])

  // Toggle between languages
  const toggleLanguage = () => {
    try {
      // First show loading overlay immediately
      showLoading(t('changing_language'))

      // Get the new language value
      const newLang = language === 'en' ? 'kh' : 'en'

      // Set a short timeout to allow the loading overlay to appear
      setTimeout(() => {
        // Then set the language (which updates localStorage/cookies)
        setLanguage(newLang)

        // Log for debugging
        console.log('Language changed to:', newLang)

        // Set flag to trigger the reload effect
        setIsChangingLanguage(true)
      }, 100) // Short delay to ensure loading overlay appears first
    } catch (error) {
      console.error('Error changing language:', error)
      hideLoading()
      // Force reload as a fallback
      window.location.reload()
    }
  }

  return (
    <button
      onClick={toggleLanguage}
      className="flex items-center justify-center min-w-[2.5rem] h-8 rounded-lg bg-white/10 border border-white/30 text-zinc-300 hover:text-white hover:bg-jade-purple/20 hover:border-jade-purple/40 transition-all duration-300 px-2 py-1"
      style={{
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
      }}
      title={language === 'en' ? 'ខ្មែរ' : 'English'}
    >
      {language === 'en' ? (
        <span className="text-xs font-semibold">ខ្មែរ</span>
      ) : (
        <span className="text-xs font-semibold">EN</span>
      )}
    </button>
  )
}
