import { motion } from 'framer-motion';
import React from 'react';

interface GlassButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  disabled?: boolean;
  href?: string;
  variant?: 'filled' | 'outline' | 'filled-flat' | 'outline-flat'; // Added flat variants
}

/**
 * GlassButton - A reusable button component with glassmorphism styling
 *
 * This component supports four variants:
 * - 'filled': Semi-transparent white background with purple border and glow effect
 * - 'outline': Transparent background with glowing purple border
 * - 'filled-flat': Semi-transparent white background with purple border (no shadow)
 * - 'outline-flat': Transparent background with purple border (no shadow)
 *
 * It adds motion effects for hover and tap interactions.
 */
export default function GlassButton({
  children,
  onClick,
  type = 'button',
  className = '',
  disabled = false,
  href,
  variant = 'filled', // Default to filled variant
}: GlassButtonProps) {
  // Determine which glass button style to use based on variant
  let buttonStyle = 'btn-glass-filled'; // Default

  switch(variant) {
    case 'outline':
      buttonStyle = 'btn-glass-outline';
      break;
    case 'filled-flat':
      buttonStyle = 'btn-glass-filled-flat';
      break;
    case 'outline-flat':
      buttonStyle = 'btn-glass-outline-flat';
      break;
    case 'filled':
    default:
      buttonStyle = 'btn-glass-filled';
  }

  // Base button component with motion effects
  const ButtonElement = (
    <motion.button
      type={type}
      className={`${buttonStyle} ${className}`}
      onClick={onClick}
      disabled={disabled}
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.98 }}
    >
      {children}
    </motion.button>
  );

  // If href is provided, wrap the button in an anchor tag
  if (href) {
    return (
      <a href={href} className="inline-block">
        {ButtonElement}
      </a>
    );
  }

  return ButtonElement;
}
