'use client'

import Hero from '@/components/Hero'
import ValueProposition from '@/components/ValueProposition'
import PricingTable from '@/components/PricingTable'
import HowItWorks from '@/components/HowItWorks'
import CallToAction from '@/components/CallToAction'
import FAQs from '@/components/FAQs'
import Footer from '@/components/Footer'
import Navbar from '@/components/Navbar'
// Import optimized combined background
import CombinedBackground from '@/components/CombinedBackground'

export default function Home() {
  return (
    <main className="min-h-screen bg-deep-blue relative">
      {/* Combined background with performance optimizations */}
      <CombinedBackground />

      {/* Test page link */}
      {/* <div className="fixed bottom-4 right-4 z-50">
        <a
          href="/test-background"
          className="bg-jade-purple/80 hover:bg-jade-purple text-white px-4 py-2 rounded-lg text-sm"
          style={{ boxShadow: '0 0 10px rgba(134, 107, 255, 0.5)' }}
        >
          Test Backgrounds
        </a>
      </div> */}

      <Navbar />
      <section id="hero">
        <Hero />
      </section>

      <section id="features">
        <ValueProposition />
      </section>

      <section id="pricing">
        <PricingTable />
      </section>

      <section id="how-it-works">
        <HowItWorks />
      </section>

      <section id="faq">
        <FAQs />
      </section>

      <CallToAction />
      <Footer />
    </main>
  )
}