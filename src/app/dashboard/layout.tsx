import { createServerComponentSupabase } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'
import DashboardWrapper from './_components/DashboardWrapper'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Server-side authentication check
  const supabase = await createServerComponentSupabase()
  const { data, error } = await supabase.auth.getUser()
  
  if (error || !data.user) {
    // Redirect to access page if not authenticated
    redirect('/access')
  }
  
  return (
    <DashboardWrapper>
      {children}
    </DashboardWrapper>
  )
} 