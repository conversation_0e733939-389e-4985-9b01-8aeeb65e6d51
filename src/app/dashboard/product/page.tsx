'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'

export default function ProductPage() {
  const [isAdding, setIsAdding] = useState(false)
  
  const products = [
    {
      id: 1,
      name: 'Premium Facial Cleanser',
      category: 'Skincare',
      price: '$29.99',
      stock: 'In Stock',
      dateAdded: 'Apr 15, 2025'
    },
    {
      id: 2,
      name: 'Anti-Aging Serum',
      category: 'Skincare',
      price: '$49.99',
      stock: 'In Stock',
      dateAdded: 'Apr 14, 2025'
    },
    {
      id: 3,
      name: 'Hydrating Face Mask',
      category: 'Masks',
      price: '$19.99',
      stock: 'Low Stock',
      dateAdded: 'Apr 10, 2025'
    },
    {
      id: 4,
      name: 'Vitamin C Brightening Cream',
      category: 'Moisturizers',
      price: '$39.99',
      stock: 'Out of Stock',
      dateAdded: 'Apr 5, 2025'
    }
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl md:text-3xl font-bold font-title">
            <span className="gradient-text">AI Brain</span> Products
          </h1>
          <Link href="/dashboard" className="text-sm text-zinc-400 hover:text-white">
            ← Back to Dashboard
          </Link>
        </div>

        <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h2 className="text-xl font-bold mb-2 font-title">Product Catalog</h2>
              <p className="text-zinc-400 text-sm mb-4 md:mb-0 font-body">
                Manage your products so the AI can accurately answer customer questions about them
              </p>
            </div>
            <button 
              className="btn-primary"
              onClick={() => setIsAdding(true)}
              disabled={isAdding}
            >
              {isAdding ? 'Adding...' : 'Add New Product'}
            </button>
          </div>

          <div className="bg-zinc-800 p-4 rounded-lg mb-6">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-jade-purple/20 rounded-full flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-jade-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-sm text-zinc-300 font-body">
                Products added here will be used by the AI to answer customer inquiries about pricing, availability, and features.
              </p>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-zinc-800">
                  <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Product Name</th>
                  <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Category</th>
                  <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Price</th>
                  <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Stock</th>
                  <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Date Added</th>
                  <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Actions</th>
                </tr>
              </thead>
              <tbody>
                {products.map((product) => (
                  <tr key={product.id} className="border-b border-zinc-800">
                    <td className="py-3 px-4 font-body">{product.name}</td>
                    <td className="py-3 px-4 font-body">{product.category}</td>
                    <td className="py-3 px-4 font-body">{product.price}</td>
                    <td className="py-3 px-4 font-body">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        product.stock === 'In Stock' 
                          ? 'bg-green-100 text-green-800' 
                          : product.stock === 'Low Stock'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                      }`}>
                        {product.stock}
                      </span>
                    </td>
                    <td className="py-3 px-4 font-body">{product.dateAdded}</td>
                    <td className="py-3 px-4 font-body">
                      <button className="text-jade-purple hover:text-jade-purple-dark mr-2">Edit</button>
                      <button className="text-red-500 hover:text-red-700">Delete</button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
          <h2 className="text-xl font-bold mb-4 font-title">Product Insights</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-zinc-800 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-2 font-title">Most Asked About</h3>
              <p className="text-xl font-bold text-white">Anti-Aging Serum</p>
              <p className="text-zinc-400 text-sm">142 customer inquiries</p>
            </div>
            <div className="bg-zinc-800 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-2 font-title">Product Coverage</h3>
              <p className="text-xl font-bold text-white">4 of 12 Products</p>
              <p className="text-zinc-400 text-sm">Add more products for better AI responses</p>
            </div>
            <div className="bg-zinc-800 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-2 font-title">AI Accuracy</h3>
              <p className="text-xl font-bold text-white">89%</p>
              <p className="text-zinc-400 text-sm">For product-related questions</p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
} 