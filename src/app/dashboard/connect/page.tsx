'use client'

import Link from 'next/link'
import { useState, useEffect, useRef } from 'react'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useAuth } from '@/context/AuthContext'
import { useDashboardData } from '@/hooks/useOptimizedData'
import Footer from '@/components/Footer'
import { FaPaperPlane, FaFacebookMessenger, FaInstagram, FaGlobe, FaChevronDown } from 'react-icons/fa'
import { FaTiktok } from 'react-icons/fa6'
import { useLanguage } from '@/context/LanguageContext'

type WebhookState = {
  [key: string]: {
    isCopied: boolean;
  };
};

// Define platform type
type Platform = 'facebook' | 'instagram' | 'whatsapp' | 'telegram' | 'web' | 'tiktok';

// Define client credentials type
type ClientCredentials = {
  id: number;
  client_id: string;
  fb_url?: string;
  ig_url?: string;
  wa_url?: string;
  tg_url?: string;
  web_url?: string;
  web_domain?: string;
  fb_token?: string;
  ig_token?: string;
  wa_token?: string;
  tg_token?: string;
  fb_status?: number;
  ig_status?: number;
  wa_status?: number;
  tg_status?: number;
  web_status?: number;
  fb_id?: string;
  ig_id?: string;
  tg_id?: string;
  wa_id?: string;
  web_name?: string;
  fb_name?: string;
  ig_name?: string;
  tg_name?: string;
  wa_name?: string;
  tg_biz_id?: string;
  created_at?: string;
  updated_at?: string;
};

export default function ConnectAccountsPage() {
  const { user: _ } = useAuth() // Unused but kept for context
  const supabase = createClientComponentClient()
  const { t } = useLanguage()

  // Use dashboard cache for client info
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo

  // State variables
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)
  const [credentials, setCredentials] = useState<ClientCredentials | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null)
  const [selectedPlatform, setSelectedPlatform] = useState<Platform>('telegram')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  const [availablePlatforms, setAvailablePlatforms] = useState<Platform[]>([])
  const [webhookStates, setWebhookStates] = useState<WebhookState>({
    'fb-webhook': { isCopied: false },
    'ig-webhook': { isCopied: false },
    'wa-webhook': { isCopied: false },
    'web-webhook': { isCopied: false },
    'privacy-policy': { isCopied: false },
    'telegram-bot-name': { isCopied: false }
  })
  const [showConfirmation, setShowConfirmation] = useState<{show: boolean, platform: string | null, action?: string | null}>({
    show: false,
    platform: null,
    action: null
  })

  // Track connected platforms
  const [connectedPlatforms, setConnectedPlatforms] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    whatsapp: false,
    telegram: false,
    web: false
  })

  // Track platform status (enabled/disabled)
  const [platformStatus, setPlatformStatus] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    whatsapp: false,
    telegram: false,
    web: false
  })

  // Store token values (used to track token changes)
  const [tokenValues, setTokenValues] = useState<{[key: string]: string}>({
    facebook: '',
    instagram: '',
    whatsapp: '',
    telegram: ''
  })



  // We're using password input type to mask tokens, no need for additional state

  // Refs for input fields
  const facebookTokenRef = useRef<HTMLInputElement>(null)
  const instagramTokenRef = useRef<HTMLInputElement>(null)
  const instagramIdRef = useRef<HTMLInputElement>(null)
  const telegramTokenRef = useRef<HTMLInputElement>(null)
  const telegramBusinessUsernameRef = useRef<HTMLInputElement>(null)
  const webDomainRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // State for Telegram connection type (bot or business)
  const [telegramConnectionType, setTelegramConnectionType] = useState<'bot' | 'business'>('bot')

  // Platform data with icons and names
  const platforms = [
    { id: 'telegram', name: t('telegram'), icon: <FaPaperPlane size={16} className="text-blue-400" /> },
    { id: 'facebook', name: t('facebook_messenger'), icon: <FaFacebookMessenger size={18} className="text-blue-500" /> },
    { id: 'instagram', name: t('instagram'), icon: <FaInstagram size={18} className="text-pink-500" /> },
    // { id: 'whatsapp', name: 'WhatsApp', icon: <FaWhatsapp size={18} className="text-green-500" /> },
    { id: 'web', name: t('web_api_coming_soon_short'), icon: <FaGlobe size={18} className="text-purple-500" />, disabled: true },
    { id: 'tiktok', name: t('tiktok_coming_soon_short'), icon: <FaTiktok size={18} className="text-white" />, disabled: true }
  ]

  // Handle platform selection
  const handleSelectPlatform = (platform: Platform) => {
    // First close the dropdown immediately for better responsiveness
    setIsDropdownOpen(false)
    // Then update the selected platform (this will trigger a re-render)
    // No need for setTimeout, we'll handle this with proper animations
    setSelectedPlatform(platform)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Disable page scroll when any popup is open
  useEffect(() => {
    if (errorMessage || successMessage || isLoading || showConfirmation.show) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [errorMessage, successMessage, isLoading, showConfirmation.show])

  // Set isClient to true when component mounts and fetch credentials
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Wait for dashboard data to load before fetching credentials and connection limit
  useEffect(() => {
    if (clientInfo?.client_id) {
      fetchClientCredentials();
      fetchConnectionLimit();
    }
  }, [clientInfo?.client_id, clientInfo?.plan_type])

  // State for connection limit
  const [connectionLimit, setConnectionLimit] = useState<number>(1);

  // Fetch connection limit from user's plan
  const fetchConnectionLimit = async () => {
    if (!clientInfo?.plan_type) return;

    try {
      const { data: planData, error } = await supabase
        .from('plans')
        .select('connections')
        .eq('name', clientInfo.plan_type)
        .single();

      if (error) {
        console.error('Error fetching plan data:', error);
        setConnectionLimit(1); // Default fallback
        return;
      }

      const limit = planData?.connections || 1;
      setConnectionLimit(limit);
    } catch (error) {
      console.error('Error in fetchConnectionLimit:', error);
      setConnectionLimit(1); // Default fallback
    }
  };

  // Get connection limit
  const getConnectionLimit = () => {
    return connectionLimit;
  };

  // Fetch client credentials from database (KEEP REAL-TIME - Security Critical)
  const fetchClientCredentials = async () => {
    try {
      setIsLoading(true)

      // Wait for dashboard data to load before fetching credentials
      if (!clientInfo?.client_id) {
        setIsLoading(false)
        return
      }

      const clientId = clientInfo.client_id

      if (!clientId) {
        setErrorMessage(t('client_id_not_found'))
        return
      }

      // Query the client_credentials table
      const { data, error } = await supabase
        .from('client_credentials')
        .select('*')
        .eq('client_id', clientId)
        .maybeSingle()

      if (error) {
        console.error('Error fetching client credentials:', error)
        setErrorMessage(`${t('failed_load_connection_data')}: ${error.message}`)
        return
      }

      // If no record exists, create a new one
      if (!data) {
        // Create a new record with empty URLs and tokens
        const { data: newData, error: insertError } = await supabase
          .from('client_credentials')
          .insert({
            client_id: clientId,
            fb_url: '',
            ig_url: '',
            wa_url: '',
            tg_url: '',
            web_url: '',
            fb_token: '',
            ig_token: '',
            wa_token: '',
            tg_token: ''
          })
          .select()
          .single()

        if (insertError) {
          console.error('Error creating client credentials record:', insertError)
          setErrorMessage(`${t('failed_initialize_connection_data')}: ${insertError.message}`)
          return
        }

        setCredentials(newData)
      } else {
        setCredentials(data)

        // Update token values
        const newTokenValues = {
          facebook: data.fb_token || '',
          instagram: data.ig_token || '',
          whatsapp: data.wa_token || '',
          telegram: data.tg_token || ''
        }
        setTokenValues(newTokenValues)

        // Set connected platforms based on token/domain presence
        const newConnectedPlatforms = {
          facebook: !!data.fb_token,
          instagram: !!data.ig_token,
          whatsapp: !!data.wa_token,
          telegram: !!data.tg_token || !!data.tg_biz_id, // Check both regular and business connections
          web: !!data.web_domain
        }
        setConnectedPlatforms(newConnectedPlatforms)

        // Set platform status based on status fields (default to 1/true if status field is not present)
        // For Telegram, use tg_status for both bot and business connections
        const telegramConnected = !!data.tg_token || !!data.tg_biz_id;
        const telegramStatus = telegramConnected ? (data.tg_status === undefined ? true : data.tg_status === 1) : false;

        const newPlatformStatus = {
          facebook: data.fb_status === undefined ? true : data.fb_status === 1,
          instagram: data.ig_status === undefined ? true : data.ig_status === 1,
          whatsapp: data.wa_status === undefined ? true : data.wa_status === 1,
          telegram: telegramStatus,
          web: data.web_status === undefined ? true : data.web_status === 1
        }
        setPlatformStatus(newPlatformStatus)

        // Determine which platforms are already connected
        const connectedPlatformsList: Platform[] = [];
        if (!!data.fb_token) connectedPlatformsList.push('facebook');
        if (!!data.ig_token) connectedPlatformsList.push('instagram');
        // if (!!data.wa_token) connectedPlatformsList.push('whatsapp');
        // Check for both regular Telegram bot and Telegram Business connections
        if (!!data.tg_token || !!data.tg_biz_id) connectedPlatformsList.push('telegram');

        // Consider web as connected if there's a web_domain
        if (!!data.web_domain) connectedPlatformsList.push('web');

        // Set available platforms (all platforms except those already connected or disabled)
        const allPlatforms: Platform[] = ['telegram', 'facebook', 'instagram', /* 'whatsapp', */ 'web', 'tiktok'];
        const available = allPlatforms.filter(p =>
          !connectedPlatformsList.includes(p) || p === 'tiktok'
        );
        setAvailablePlatforms(available);

        // Set the default selected platform to the first available one
        if (available.length > 0 && available[0] !== 'tiktok') {
          setSelectedPlatform(available[0]);
        }

        // Populate input fields with token values
        if (facebookTokenRef.current && data.fb_token) {
          facebookTokenRef.current.value = data.fb_token
        }
        if (instagramTokenRef.current && data.ig_token) {
          instagramTokenRef.current.value = data.ig_token
        }
        if (telegramTokenRef.current && data.tg_token) {
          telegramTokenRef.current.value = data.tg_token
        }
      }
    } catch (error) {
      console.error('Unexpected error in fetchClientCredentials:', error)
      setErrorMessage(t('unexpected_error'))
    } finally {
      setIsLoading(false)
    }
  }

  // This function gets the webhook URL without exposing it in the DOM
  const getWebhookUrl = (id: string): string => {
    // Get the appropriate URL based on the webhook ID
    if (id === 'fb-webhook' && credentials?.fb_url) {
      return credentials.fb_url;
    } else if (id === 'ig-webhook' && credentials?.ig_url) {
      return credentials.ig_url;
    } else if (id === 'wa-webhook' && credentials?.wa_url) {
      return credentials.wa_url;
    } else if (id === 'web-webhook' && credentials?.web_url) {
      return credentials.web_url;
    } else if (id === 'privacy-policy') {
      return 'https://www.chhlatbot.com/privacy';
    } else {
      // Fallback to default URLs if credentials are not available
      if (id === 'fb-webhook') return 'https://api.chhlatbot.com/webhook/messenger';
      else if (id === 'ig-webhook') return 'https://api.chhlatbot.com/webhook/instagram';
      else if (id === 'wa-webhook') return 'https://api.chhlatbot.com/webhook/whatsapp';
      else if (id === 'web-webhook') return 'https://api.chhlatbot.com/webhook/web';
    }
    return '';
  };

  const handleCopyWebhook = async (id: string) => {
    try {
      // Get the URL directly from the function, not storing it in a variable in the component
      const url = getWebhookUrl(id);

      // Copy to clipboard
      await navigator.clipboard.writeText(url);

      // Show success state
      setWebhookStates(prev => ({
        ...prev,
        [id]: { isCopied: true }
      }));

      // Reset copy status after 1.5 seconds
      setTimeout(() => {
        setWebhookStates(prev => ({
          ...prev,
          [id]: { isCopied: false }
        }));
      }, 1500);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  // Handle copying bot name for Telegram Business
  const handleCopyBotName = async () => {
    try {
      const botName = 'yourchhlatbot';
      await navigator.clipboard.writeText(botName);

      // Show success state using existing webhook states
      setWebhookStates(prev => ({
        ...prev,
        'telegram-bot-name': { isCopied: true }
      }));

      // Reset copy status after 1.5 seconds
      setTimeout(() => {
        setWebhookStates(prev => ({
          ...prev,
          'telegram-bot-name': { isCopied: false }
        }));
      }, 1500);
    } catch (err) {
      console.error('Failed to copy bot name:', err);
    }
  };

  // We've removed the toggleWebhookVisibility function as it's no longer needed

  // Handle secure token input changes
  const handleTokenChange = (platform: string, value: string) => {
    // Store the actual token value in the ref
    switch (platform) {
      case 'facebook':
        if (facebookTokenRef.current) facebookTokenRef.current.value = value;
        break;
      case 'instagram':
        if (instagramTokenRef.current) instagramTokenRef.current.value = value;
        break;
      /* case 'whatsapp':
        if (whatsappTokenRef.current) whatsappTokenRef.current.value = value;
        break; */
      case 'telegram':
        if (telegramTokenRef.current) telegramTokenRef.current.value = value;
        break;
    }
  }

  // Initiate toggle platform status - show confirmation popup
  const initiateTogglePlatformStatus = (platform: string) => {
    // Get current status
    const currentStatus = platformStatus[platform];
    const newStatus = !currentStatus;

    // Show confirmation dialog
    setShowConfirmation({
      show: true,
      platform,
      action: newStatus ? 'enable' : 'disable'
    });
  };

  // Handle toggling platform status after confirmation - simplified with new API
  const handleTogglePlatformStatus = async (platform: string) => {
    try {
      // Hide confirmation dialog
      setShowConfirmation({
        show: false,
        platform: null
      });

      setIsLoading(true);

      // Get current status
      const currentStatus = platformStatus[platform];
      const newStatus = !currentStatus;

      // Call the new centralized toggle API
      const response = await fetch('/api/platform/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: platform,
          status: newStatus
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        // Handle specific error messages from backend
        setErrorMessage(responseData.error || `Failed to update ${platform} status`);
        return;
      }

      if (!responseData.success) {
        setErrorMessage(`Unexpected response when updating ${platform} status`);
        return;
      }

      // API call successful - update local state and show success

      // Update local state
      setPlatformStatus(prev => ({
        ...prev,
        [platform]: newStatus
      }));

      // Show success message
      const status = newStatus ? t('enable').toLowerCase() : t('disable').toLowerCase();
      setSuccessMessage(t('platform_status_changed').replace('{platform}', platform).replace('{status}', status));

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 2000);

    } catch (error) {
      console.error(`Error toggling ${platform} status:`, error);
      setErrorMessage(`Failed to update ${platform} status`);

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Show confirmation dialog before connecting
  const initiateConnect = (platform: string) => {
    // Check if we've reached the connection limit
    const connectedCount = Object.values(connectedPlatforms).filter(Boolean).length;

    // Check if this platform is already connected
    const isAlreadyConnected = connectedPlatforms[platform];

    const connectionLimit = getConnectionLimit();
    if (connectedCount >= connectionLimit && !isAlreadyConnected) {
      setErrorMessage(t('connection_limit_reached_error').replace('{limit}', connectionLimit.toString()));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);

      return;
    }

    // Get token or domain from input ref
    let value = '';
    let instagramId = '';

    switch (platform) {
      case 'facebook':
        value = facebookTokenRef.current?.value || '';
        break;
      case 'instagram':
        value = instagramTokenRef.current?.value || '';
        instagramId = instagramIdRef.current?.value || '';
        break;
      /* case 'whatsapp':
        value = whatsappTokenRef.current?.value || '';
        whatsappId = whatsappIdRef.current?.value || '';
        break; */
      case 'telegram':
        if (telegramConnectionType === 'bot') {
          value = telegramTokenRef.current?.value || '';
        } else {
          // For Telegram Business, get username instead of token
          value = telegramBusinessUsernameRef.current?.value || '';
        }
        break;
      case 'web':
        value = webDomainRef.current?.value || '';
        break;
    }

    if (!value || value.trim() === '') {
      let errorKey = 'enter_valid_token';
      if (platform === 'web') {
        errorKey = 'enter_valid_domain';
      } else if (platform === 'telegram' && telegramConnectionType === 'business') {
        errorKey = 'enter_valid_telegram_username';
      }
      setErrorMessage(t(errorKey).replace('{platform}', platform));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 1000);

      return;
    }

    // Check for Instagram ID
    if (platform === 'instagram' && (!instagramId || instagramId.trim() === '')) {
      setErrorMessage(t('enter_valid_page_id'));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 1000);

      return;
    }

    // Check for WhatsApp ID
    /* if (platform === 'whatsapp' && (!whatsappId || whatsappId.trim() === '')) {
      setErrorMessage('Please enter a valid ID for WhatsApp.');

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 1000);

      return;
    } */

    // Show confirmation dialog
    setShowConfirmation({
      show: true,
      platform,
      action: null // No action means it's a connect operation
    });
  };

  // Handle platform connection after confirmation
  const handleConnect = async (platform: string) => {
    try {
      // Hide confirmation dialog
      setShowConfirmation({
        show: false,
        platform: null
      });

      // Clear any previous messages
      setErrorMessage(null);
      setSuccessMessage(null);

      // Get client ID from dashboard cache
      const clientId = clientInfo?.client_id;

      if (!clientId) {
        setErrorMessage(t('client_id_not_found'));
        return;
      }

      // Get token or domain from input ref
      let token = '';
      let domain = '';
      let webhookUrl = '';
      let isWebPlatform = platform === 'web';

      switch (platform) {
        case 'facebook':
          token = facebookTokenRef.current?.value || '';
          webhookUrl = credentials?.fb_url || 'https://api.chhlatbot.com/webhook/messenger';
          break;
        case 'instagram':
          token = instagramTokenRef.current?.value || '';
          webhookUrl = credentials?.ig_url || 'https://api.chhlatbot.com/webhook/instagram';
          break;
        /* case 'whatsapp':
          token = whatsappTokenRef.current?.value || '';
          webhookUrl = credentials?.wa_url || 'https://api.chhlatbot.com/webhook/whatsapp';
          break; */
        case 'telegram':
          if (telegramConnectionType === 'bot') {
            token = telegramTokenRef.current?.value || '';
            webhookUrl = credentials?.tg_url || ''; // Use tg_url from credentials
          } else {
            // For Telegram Business, use username as URL and leave token empty
            const username = telegramBusinessUsernameRef.current?.value || '';
            // Ensure username starts with @
            const formattedUsername = username.startsWith('@') ? username : `@${username}`;
            token = ''; // Leave token empty for business
            webhookUrl = formattedUsername; // Use username as URL
          }
          break;
        case 'web':
          domain = webDomainRef.current?.value || '';
          webhookUrl = credentials?.web_url || 'https://api.chhlatbot.com/webhook/web';
          break;
        default:
          webhookUrl = '';
      }

      // Check validation before proceeding - handle Telegram Business first
      if (platform === 'telegram' && telegramConnectionType === 'business') {
        // For Telegram Business, check username instead of token
        const username = telegramBusinessUsernameRef.current?.value || '';
        if (!username || username.trim() === '') {
          setErrorMessage(t('enter_valid_telegram_username'));

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
          }, 3000);

          return;
        }
      } else if (isWebPlatform) {
        // Check domain for web platforms
        if (!domain || domain.trim() === '') {
          setErrorMessage(t('enter_valid_domain').replace('{platform}', platform));

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
          }, 1500);

          return;
        }
      } else {
        // For all other platforms (including Telegram Bot), check token
        if (!token || token.trim() === '') {
          setErrorMessage(t('enter_valid_token').replace('{platform}', platform));

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
          }, 3000);

          return;
        }
      }

      // Set connecting platform
      setConnectingPlatform(platform);

      // Prepare request body
      let platformType = platform;

      // For Telegram Business, use telegram_biz as type
      if (platform === 'telegram' && telegramConnectionType === 'business') {
        platformType = 'telegram_biz';
      }

      // For Instagram, append the page ID to the type parameter if available
      if (platform === 'instagram' && instagramIdRef.current?.value) {
        platformType = `${platform}/${instagramIdRef.current.value}`;
      }

      // For WhatsApp, append the ID to the type parameter if available
      /* if (platform === 'whatsapp' && whatsappIdRef.current?.value) {
        platformType = `${platform}/${whatsappIdRef.current.value}`;
      } */

      const requestBody: any = {
        client_id: clientId,
        webhook_url: webhookUrl,
        token: isWebPlatform ? domain : token,
        type: platformType
      };

      // For Telegram Business, the webhook_url contains the username and token is empty
      // No additional fields needed as the username is already in webhook_url

      // Send to API endpoint
      const response = await fetch('/api/webhooks/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      // Wait for a minimum of 1 second for UX purposes
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Parse the response
      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || t('failed_connect_generic').replace('{platform}', platform));
      }

      // Make sure it's a success
      if (!responseData.success) {
        throw new Error(t('unexpected_server_response').replace('{platform}', platform));
      }



      // Show success message
      setSuccessMessage(t('successfully_connected').replace('{platform}', platform));

      // Wait for success message to be shown, then reload the page
      setTimeout(() => {
        window.location.reload();
      }, 1500);

    } catch (error) {
      console.error(`Error connecting ${platform}:`, error);
      setErrorMessage(t('failed_connect_platform').replace('{platform}', platform));

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    } finally {
      // Reset connecting platform
      setConnectingPlatform(null);
    }
  };

  // Don't render anything server-side
  if (!isClient) return null

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative pb-16">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden"
          >

            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <Link href="/dashboard" className="group">
                <img
                  src="/images/white_tran_logo.svg"
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Error Message - Positioned in the middle of the window */}
      {errorMessage && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50 bg-black/50"
        >
          <div
            className="bg-black/80 border border-red-500/50 rounded-xl p-6 max-w-md mx-auto transform transition-all relative"
            style={{
              willChange: 'transform, opacity'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={() => setErrorMessage(null)}
              className="absolute top-3 right-3 w-8 h-8 bg-red-500/20 hover:bg-jade-purple rounded-full flex items-center justify-center text-red-400 hover:text-white transition-all duration-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="flex items-center justify-center mb-4">
              <div className="bg-red-500/20 rounded-full p-3">
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-white text-center mb-2 font-title">{t('error')}</h3>
            <p className="text-center text-red-200 font-body">{errorMessage}</p>
          </div>
        </div>
      )}

      {/* Success Message - Positioned in the middle of the window */}
      {successMessage && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50 bg-black/50"
        >
          <div
            className="bg-black/80 border border-green-500/50 rounded-xl p-6 max-w-md mx-auto transform transition-all"
            style={{
              willChange: 'transform, opacity'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            {/* <button
              onClick={() => setSuccessMessage(null)}
              className="absolute top-3 right-3 w-8 h-8 bg-green-500/20 hover:bg-jade-purple rounded-full flex items-center justify-center text-green-400 hover:text-white transition-all duration-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button> */}
            <div className="flex items-center justify-center mb-4">
              <div className="bg-green-500/20 rounded-full p-3">
                <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-white text-center mb-2 font-title">{t('success')}</h3>
            <p className="text-center text-green-200 font-body">{successMessage}</p>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {isLoading && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50 bg-black/50"
        >
          <div
            className="bg-black/80 border border-jade-purple/50 rounded-xl p-6 max-w-md mx-auto transform transition-all"
            style={{
              willChange: 'transform, opacity'
            }}
          >
            <div className="flex items-center justify-center mb-4">
              <div className="bg-jade-purple/20 rounded-full p-3">
                <svg className="animate-spin w-8 h-8 text-jade-purple" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-white text-center mb-2 font-title">{t('loading')}</h3>
            <p className="text-center text-white/80 font-body">{t('please_wait_processing')}</p>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      {showConfirmation.show && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-white/20 overflow-hidden"
            style={{
              willChange: 'transform, opacity'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative z-10">
            {/* Close Button */}
            {/* <button
              onClick={() => setShowConfirmation({ show: false, platform: null })}
              className="absolute top-3 right-3 w-8 h-8 bg-white/10 hover:bg-jade-purple rounded-full flex items-center justify-center text-zinc-400 hover:text-white transition-all duration-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <div className="flex justify-center mb-4">
              <div className="bg-white/25 text-zinc-300 rounded-full p-3">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div> */}
            <h3 className="text-xl font-bold mb-4 font-title text-center">
              {showConfirmation.action ? t('confirm_status_change') : t('confirm_connection')}
            </h3>
            <p className="text-zinc-300 mb-6 text-center">
              {showConfirmation.action ? (
                t('confirm_action_platform').replace('{action}', showConfirmation.action).replace('{platform}', showConfirmation.platform || '')
              ) : (
                showConfirmation.platform === 'web'
                  ? t('confirm_connect_web').replace('{platform}', showConfirmation.platform || '')
                  : t('confirm_connect_platform').replace('{platform}', showConfirmation.platform || '')
              )}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmation({ show: false, platform: null })}
                className="flex-1 py-2 bg-black/30 hover:bg-black/40 hover:border-white/50 text-white rounded-lg transition-colors border border-white/20"
              >
                {t('cancel')}
              </button>
              <button
                onClick={() => {
                  if (showConfirmation.action) {
                    // Handle toggle action
                    handleTogglePlatformStatus(showConfirmation.platform!);
                  } else {
                    // Handle connect action
                    handleConnect(showConfirmation.platform!);
                  }
                }}
                className="flex-1 py-2 bg-jade-purple text-white hover:bg-jade-purple-dark hover:border-jade-purple transition-all duration-200 rounded-lg font-medium border border-white/20"
              >
                {showConfirmation.action ? (showConfirmation.action === 'enable' ? t('enable') : t('disable')) : t('connect')}
              </button>
            </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex-grow container mx-auto px-4 py-8">
        {/* Animation commented out as requested */}
        <div>
          {/* Content header with title and back button */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-2">
              <Link
                href="/dashboard"
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-zinc-300 hover:text-white bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-all duration-200 group"
              >
                <svg
                  className="w-4 h-4 transform -translate-x-0.5 group-hover:-translate-x-1 transition-transform"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                {t('connect_accounts')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Connected Platforms Section - Moved to the top */}
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
              style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.20)'
              }}>
            <div className="relative z-10">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-xl font-bold font-title">{t('connected_platforms')} ({Object.values(connectedPlatforms).filter(Boolean).length}/{getConnectionLimit()})</h2>
                {/* <p className="text-zinc-400 text-sm font-body mt-1">
                  {t('manage_connected_platforms')} ({Object.values(connectedPlatforms).filter(Boolean).length}/{connectionsLimit})
                </p> */}
              </div>
              {Object.values(connectedPlatforms).filter(Boolean).length > 0 && (
                <Link href="/dashboard/connect/editConnection" className="text-zinc-400 hover:text-white px-3 py-1 rounded hover:bg-white/10 transition-colors text-sm font-body">
                  {t('edit')}
                </Link>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              {/* Facebook */}
              {connectedPlatforms.facebook && (
                <div className="bg-black/5 border border-white/20 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-black/40 rounded-full flex items-center justify-center mr-4 text-blue-500 shadow-lg border border-white/20">
                      <FaFacebookMessenger size={18} />
                    </div>
                    <div>
                      <h3 className="font-medium text-white text-base font-title">{t('facebook_messenger')}</h3>
                      <p className="text-zinc-400 text-xs font-body truncate max-w-[150px]">{t('name')}: {credentials?.fb_name || t('not_set')}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-facebook"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.facebook}
                        onChange={() => initiateTogglePlatformStatus('facebook')}
                      />
                      <label
                        htmlFor="toggle-facebook"
                        className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${platformStatus.facebook ? 'bg-green-500/30' : 'bg-gray-700'}`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${platformStatus.facebook ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                  </div>
                </div>
              )}

              {/* Instagram */}
              {connectedPlatforms.instagram && (
                <div className="bg-black/5 border border-white/20 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-black/40 rounded-full flex items-center justify-center mr-4 text-pink-500 shadow-lg border border-white/20">
                      <FaInstagram size={18} />
                    </div>
                    <div>
                      <h3 className="font-medium text-white text-base font-title">{t('instagram')}</h3>
                      <p className="text-zinc-400 text-xs font-body truncate max-w-[150px]">{t('name')}: {credentials?.ig_name || t('not_set')}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-instagram"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.instagram}
                        onChange={() => initiateTogglePlatformStatus('instagram')}
                      />
                      <label
                        htmlFor="toggle-instagram"
                        className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${platformStatus.instagram ? 'bg-green-500/30' : 'bg-gray-700'}`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${platformStatus.instagram ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                  </div>
                </div>
              )}

              {/* WhatsApp */}
              {/* {connectedPlatforms.whatsapp && (
                <div className="bg-deep-blue/40 backdrop-blur-lg border border-white/20 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center mr-4 text-green-500 shadow-lg border border-white/20">
                      <FaWhatsapp size={18} />
                    </div>
                    <div>
                      <h3 className="font-medium text-white text-base font-title">WhatsApp</h3>
                      <p className="text-zinc-400 text-xs font-body">Name: {credentials?.wa_name || 'Not set'}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-whatsapp"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.whatsapp}
                        onChange={() => initiateTogglePlatformStatus('whatsapp')}
                      />
                      <label
                        htmlFor="toggle-whatsapp"
                        className="block h-6 overflow-hidden rounded-full bg-gray-700 cursor-pointer"
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-transform duration-200 ease-in-out ${platformStatus.whatsapp ? 'bg-jade-purple translate-x-6' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                </div>
              )} */}

              {/* Telegram */}
              {connectedPlatforms.telegram && (
                <div className="bg-black/5 border border-white/20 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-black/40 rounded-full flex items-center justify-center mr-4 text-blue-400 shadow-lg border border-white/20">
                      <FaPaperPlane size={16} />
                    </div>
                    <div>
                      <h3 className="font-medium text-white text-base font-title">
                        {t('telegram')} {credentials?.tg_biz_id ? '(Business)' : '(Bot)'}
                      </h3>
                      <p className="text-zinc-400 text-xs font-body truncate max-w-[150px]">
                        {t('name')}: {credentials?.tg_name || t('not_set')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-telegram"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.telegram}
                        onChange={() => initiateTogglePlatformStatus('telegram')}
                      />
                      <label
                        htmlFor="toggle-telegram"
                        className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${platformStatus.telegram ? 'bg-green-500/30' : 'bg-gray-700'}`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${platformStatus.telegram ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                  </div>
                </div>
              )}

              {/* Web API */}
              {connectedPlatforms.web && (
                <div className="bg-black/5 border border-white/20 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-black/40 rounded-full flex items-center justify-center mr-4 text-purple-500 shadow-lg border border-white/20">
                      <FaGlobe size={18} />
                    </div>
                    <div>
                      <h3 className="font-medium text-white text-base font-title">{t('web_api')}</h3>
                      <p className="text-zinc-400 text-xs font-body truncate max-w-[150px]">{t('domain')}: {credentials?.web_domain || t('not_set')}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-web"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.web}
                        onChange={() => initiateTogglePlatformStatus('web')}
                      />
                      <label
                        htmlFor="toggle-web"
                        className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${platformStatus.web ? 'bg-green-500/30' : 'bg-gray-700'}`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${platformStatus.web ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                  </div>
                </div>
              )}

              {/* No connected platforms message */}
              {Object.values(connectedPlatforms).filter(Boolean).length === 0 && (
                <div className="col-span-2 bg-deep-blue/40 border border-white/20 rounded-lg p-6 text-center">
                  <p className="text-zinc-400 font-body">{t('no_platforms_connected')}</p>
                </div>
              )}
            </div>
            </div>
          </div>

          {/* Platform Selector Dropdown */}
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
          >
            <div className="relative z-10">
            <h2 className="text-xl font-bold mb-4 font-title">{t('select_platform')}</h2>
            {/* <p className="text-zinc-400 mb-6 font-body">
              {t('choose_platform_details')}
            </p> */}

            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit() && setIsDropdownOpen(!isDropdownOpen)}
                className={`w-full bg-black/30 border border-white/20 rounded-lg px-4 py-3 text-white flex items-center justify-between focus:outline-none transition-colors ${
                  Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit()
                    ? 'focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 hover:bg-black/40'
                    : 'opacity-70 cursor-not-allowed'
                }`}
                disabled={Object.values(connectedPlatforms).filter(Boolean).length >= getConnectionLimit()}
              >
                {Object.values(connectedPlatforms).filter(Boolean).length >= getConnectionLimit() ? (
                  <div className="flex items-center text-red-300">
                    <span className="font-body">{t('connection_limit_reached')}</span>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center">
                      {platforms.find(p => p.id === selectedPlatform)?.icon}
                      <span className="ml-3 font-body">
                        {platforms.find(p => p.id === selectedPlatform)?.name}
                      </span>
                    </div>
                    <FaChevronDown className={`transition-transform duration-200 ${isDropdownOpen ? 'transform rotate-180' : ''}`} />
                  </>
                )}
              </button>

              {/* Animation commented out as requested */}
              <div
                className={`relative w-full bg-deep-blue/80 border border-white/20 rounded-lg overflow-hidden mt-2 z-10 ${!isDropdownOpen ? 'hidden' : ''}`}
              >
                <ul>
                  {platforms
                    .filter(platform => availablePlatforms.includes(platform.id as Platform))
                    .map((platform) => (
                      <li key={platform.id}>
                        <button
                          onClick={() => !platform.disabled && handleSelectPlatform(platform.id as Platform)}
                          className={`w-full px-4 py-3 text-left flex items-center hover:bg-jade-purple/20 transition-colors ${
                            platform.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                          } ${selectedPlatform === platform.id ? 'bg-jade-purple/30' : ''}`}
                          disabled={platform.disabled}
                        >
                          {platform.icon}
                          <span className="ml-3 font-body">{platform.name}</span>
                        </button>
                      </li>
                    ))}
                </ul>
              </div>
            </div>
            </div>
          </div>

          {availablePlatforms.length > 0 && availablePlatforms.some(p => p !== 'tiktok') &&
           Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit() && (
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-12 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
            >
              <div className="relative z-10">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h2 className="text-xl font-bold font-title">{t('selected_platform')}</h2>
                  <p className="text-zinc-400 text-sm font-body mt-1">
                    {t('connect_platform_message').replace('{platform}', platforms.find(p => p.id === selectedPlatform)?.name || '')}
                  </p>
                </div>
              </div>



            <div className="space-y-6">

              {/* Facebook Messenger */}
              {selectedPlatform === 'facebook' && (
                <div className="bg-deep-blue/80 border border-white/30 rounded-xl p-6 hover:border-white/40 transition-all">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center mr-4 text-blue-500 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                    <FaFacebookMessenger size={18} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <h3 className="font-medium text-white text-lg font-title">{t('facebook_messenger')}</h3>
                    <p className="text-zinc-400 text-sm font-body">{t('copy_webhook_provide_token')}</p>
                  </div>
                </div>
                <div className="space-y-3">

                  {/* Privacy Policy and Webhook URLs */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    {/* Privacy Policy Link */}
                    <div className="relative w-full sm:w-[40%]">
                      <div className="flex items-center">
                        <div
                          className="w-full bg-black/30 border border-white/20 rounded-lg px-4 pr-12 py-2.5 text-sm text-zinc-400 font-body flex items-center overflow-hidden"
                          style={{
                            boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                          }}
                        >
                          <span className="text-jade-purple truncate block">https://www.chhlatbot.com/privacy</span>
                        </div>
                        <div className="absolute right-4 flex gap-2">
                          <button
                            className="text-zinc-400 hover:text-white"
                            onClick={() => handleCopyWebhook('privacy-policy')}
                            title="Copy Privacy Policy URL"
                          >
                            {webhookStates['privacy-policy'].isCopied ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            )}
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Webhook URL */}
                    <div className="relative flex-1">
                      <div className="flex items-center">
                        <div
                          className="w-full bg-black/30 border border-white/20 rounded-lg px-4 pr-12 py-2.5 text-sm text-zinc-400 font-body"
                          style={{
                            boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                          }}
                        >
                          ••••••••••••••••••
                        </div>
                        <div className="absolute right-4 flex gap-2">
                          <button
                            className="text-zinc-400 hover:text-white"
                            onClick={() => handleCopyWebhook('fb-webhook')}
                            title="Copy webhook URL"
                          >
                            {webhookStates['fb-webhook'].isCopied ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    {connectedPlatforms.facebook ? (
                      <>
                        <div className="flex-1 bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm text-zinc-400 font-body"
                          style={{
                            boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                          }}>
                          ••••••••••••••••••••••••••••••••••••••••••••••••••••
                        </div>
                        <button
                          className="bg-jade-purple/50 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          style={{
                            boxShadow: '0 0 10px rgba(134, 107, 255, 0.2)'
                          }}
                          disabled
                        >
                          {t('connected')}
                        </button>
                      </>
                    ) : (
                      <>
                        <div className="relative flex-1">
                          <input
                            ref={facebookTokenRef}
                            type="password"
                            placeholder={t('enter_access_token')}
                            defaultValue={''}
                            className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-base focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                            style={{
                              boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)',
                              fontSize: '16px'
                            }}
                            disabled={connectingPlatform === 'facebook'}
                            onChange={(e) => handleTokenChange('facebook', e.target.value)}
                          />
                        </div>
                        <button
                          className="bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          style={{
                            boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                          }}
                          onClick={() => initiateConnect('facebook')}
                          disabled={connectingPlatform === 'facebook'}
                        >
                          {connectingPlatform === 'facebook' ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              {t('connecting')}
                            </>
                          ) : (
                            t('connect')
                          )}
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              )}

              {/* Instagram DM */}
              {selectedPlatform === 'instagram' && (
                <div className="bg-deep-blue/80 border border-white/30 rounded-xl p-6 hover:border-white/40 transition-all">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center mr-4 text-pink-500 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                    <FaInstagram size={20} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <h3 className="font-medium text-white text-lg font-title">{t('instagram_dm')}</h3>
                    <p className="text-zinc-400 text-sm font-body">{t('copy_webhook_provide_token_id')}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="relative">
                    <div className="flex items-center">
                      <div
                        className="flex-1 bg-black/30 border border-white/20 rounded-lg px-4 pr-12 py-2.5 text-sm text-zinc-400 font-body"
                        style={{
                          boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                        }}
                      >
                        ••••••••••••••••••
                      </div>
                      <div className="absolute right-4 flex gap-2">
                        <button
                          className="text-zinc-400 hover:text-white"
                          onClick={() => handleCopyWebhook('ig-webhook')}
                          title="Copy webhook URL"
                        >
                          {webhookStates['ig-webhook'].isCopied ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    {connectedPlatforms.instagram ? (
                      <>
                        <div className="flex-1 bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm text-zinc-400 font-body overflow-hidden"
                          style={{
                            boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                          }}
                          title={`Token: ${tokenValues.instagram}`}>
                          <div className="truncate">•••••••••••••••••••••••••••</div>
                        </div>
                        <button
                          className="bg-jade-purple/50 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          style={{
                            boxShadow: '0 0 10px rgba(134, 107, 255, 0.2)'
                          }}
                          disabled
                        >
                          {t('connected')}
                        </button>
                      </>
                    ) : (
                      <>
                        <div className="relative w-full sm:w-[40%]">
                          <input
                            ref={instagramIdRef}
                            type="password"
                            placeholder={t('enter_id')}
                            defaultValue={''}
                            className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-base focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                            style={{
                              boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)',
                              fontSize: '16px'
                            }}
                            disabled={connectingPlatform === 'instagram'}
                          />
                        </div>
                        <div className="flex flex-col gap-2 flex-1">
                          <div className="relative">
                            <input
                              ref={instagramTokenRef}
                              type="password"
                              placeholder={t('enter_access_token')}
                              defaultValue={''}
                              className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-base focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                              style={{
                                boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)',
                                fontSize: '16px'
                              }}
                              disabled={connectingPlatform === 'instagram'}
                              onChange={(e) => handleTokenChange('instagram', e.target.value)}
                            />
                          </div>
                        </div>
                        <button
                          className="bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          style={{
                            boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                          }}
                          onClick={() => initiateConnect('instagram')}
                          disabled={connectingPlatform === 'instagram'}
                        >
                          {connectingPlatform === 'instagram' ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              {t('connecting')}
                            </>
                          ) : (
                            t('connect')
                          )}
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              )}

              {/* WhatsApp */}
              {/* {selectedPlatform === 'whatsapp' && (
                <div className="bg-deep-blue/60 backdrop-blur-lg border border-white/30 rounded-xl p-6 hover:border-white/50 transition-all" style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 10px rgba(255, 255, 255, 0.1)'
                }}>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-white/10  rounded-full flex items-center justify-center mr-4 text-green-500 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                    <FaWhatsapp size={22} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <h3 className="font-medium text-white text-lg font-title">WhatsApp</h3>
                    <p className="text-zinc-400 text-sm font-body">Copy webhook URL and provide your access token and ID</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="relative">
                    <div className="flex items-center">
                      <div
                        className="flex-1 bg-black/30 border border-white/20 rounded-lg px-4 pr-12 py-2.5 text-sm text-zinc-400 font-body"
                        style={{
                          boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                        }}
                      >
                        ••••••••••••••••••
                      </div>
                      <div className="absolute right-4 flex gap-2">
                        <button
                          className="text-zinc-400 hover:text-white"
                          onClick={() => handleCopyWebhook('wa-webhook')}
                          title="Copy webhook URL"
                        >
                          {webhookStates['wa-webhook'].isCopied ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    {connectedPlatforms.whatsapp ? (
                      <>
                        <div className="flex-1 bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm text-zinc-400 font-body overflow-hidden"
                          style={{
                            boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                          }}>
                          <div className="truncate">••••••••••••••••••••••••••••••••••••••••</div>
                        </div>
                        <button
                          className="bg-jade-purple/50 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          style={{
                            boxShadow: '0 0 10px rgba(134, 107, 255, 0.2)'
                          }}
                          disabled
                        >
                          {t('connected')}
                        </button>
                      </>
                    ) : (
                      <>
                        <div className="relative w-full sm:w-[40%]">
                          <input
                            ref={whatsappIdRef}
                            type="password"
                            placeholder="Enter ID"
                            defaultValue={''}
                            className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                            style={{
                              boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                            }}
                            disabled={connectingPlatform === 'whatsapp'}
                          />
                        </div>
                        <div className="flex flex-col gap-2 flex-1">
                          <div className="relative">
                            <input
                              ref={whatsappTokenRef}
                              type="password"
                              placeholder="Enter Access Token"
                              defaultValue={''}
                              className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                              style={{
                                boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                              }}
                              disabled={connectingPlatform === 'whatsapp'}
                              onChange={(e) => handleTokenChange('whatsapp', e.target.value)}
                            />
                          </div>
                        </div>
                        <button
                          className="bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          style={{
                            boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                          }}
                          onClick={() => initiateConnect('whatsapp')}
                          disabled={connectingPlatform === 'whatsapp'}
                        >
                          {connectingPlatform === 'whatsapp' ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              {t('connecting')}
                            </>
                          ) : (
                            t('connect')
                          )}
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              )} */}

              {/* Telegram */}
              {selectedPlatform === 'telegram' && (
                <div className="bg-deep-blue/80 border border-white/30 rounded-xl p-6 hover:border-white/40 transition-all">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center mr-4 text-blue-400 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                      <FaPaperPlane size={16} />
                    </div>
                    <div style={{ flex: 1 }}>
                      <h3 className="font-medium text-white text-lg font-title">{t('telegram')}</h3>
                      <p className="text-zinc-400 text-sm font-body">{t('choose_connection_type')}</p>
                    </div>
                  </div>

                  {/* Tab Navigation */}
                  <div className="flex mb-6 bg-black/20 rounded-lg p-1">
                    <button
                      onClick={() => setTelegramConnectionType('bot')}
                      className={`flex-1 py-2 px-4 rounded-l-md text-sm font-medium transition-all duration-200 ${
                        telegramConnectionType === 'bot'
                          ? 'bg-white/20 text-white border border-white/20'
                          : 'text-zinc-400 hover:text-white hover:bg-white/5 border border-white/20'
                      }`}
                    >
                      {t('telegram_bot')}
                    </button>
                    <button
                      onClick={() => setTelegramConnectionType('business')}
                      className={`flex-1 py-2 px-4 rounded-r-md text-sm font-medium transition-all duration-200 ${
                        telegramConnectionType === 'business'
                          ? 'bg-white/20 text-white border border-white/20'
                          : 'text-zinc-400 hover:text-white hover:bg-white/5 border border-white/20'
                      }`}
                    >
                      {t('telegram_business')}
                    </button>
                  </div>

                  {/* Tab Content */}
                  {telegramConnectionType === 'bot' && (
                    <div className="space-y-3">
                      <div className="flex flex-col sm:flex-row gap-3">
                        {connectedPlatforms.telegram ? (
                          <>
                            <div className="flex-1 bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm text-zinc-400 font-body overflow-hidden"
                              style={{
                                boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                              }}>
                              <div className="truncate">••••••••••••••••••••••••••••••••••••••••</div>
                            </div>
                            <button
                              className="bg-jade-purple/50 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                              style={{
                                boxShadow: '0 0 10px rgba(134, 107, 255, 0.2)'
                              }}
                              disabled
                            >
                              {t('connected')}
                            </button>
                          </>
                        ) : (
                          <>
                            <div className="relative flex-1">
                              <input
                                ref={telegramTokenRef}
                                type="password"
                                placeholder={t('enter_telegram_bot_token')}
                                defaultValue={''}
                                className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-base focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                                style={{
                                  boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)',
                                  fontSize: '16px'
                                }}
                                disabled={connectingPlatform === 'telegram'}
                                onChange={(e) => handleTokenChange('telegram', e.target.value)}
                              />
                            </div>
                            <button
                              className="bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                              style={{
                                boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                              }}
                              onClick={() => initiateConnect('telegram')}
                              disabled={connectingPlatform === 'telegram'}
                            >
                              {connectingPlatform === 'telegram' ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  {t('connecting')}
                                </>
                              ) : (
                                t('connect')
                              )}
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  )}

                  {telegramConnectionType === 'business' && (
                    <div className="space-y-3">
                      {/* Bot Name Copy Field */}
                      <div className="flex gap-3">
                        <div className="relative flex-1">
                          <div
                            className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm text-zinc-300 font-body flex items-center justify-between"
                            style={{
                              boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                            }}
                          >
                            <span className="text-jade-purple">yourchhlatbot</span>
                            <button
                              className="text-zinc-400 hover:text-white transition-colors flex items-center justify-center"
                              onClick={handleCopyBotName}
                              title="Copy bot name"
                            >
                              {webhookStates['telegram-bot-name']?.isCopied ? (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                              )}
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Username Input and Connect Button */}
                      <div className="flex flex-col sm:flex-row gap-3">
                        <div className="relative flex-1">
                          <input
                            ref={telegramBusinessUsernameRef}
                            type="text"
                            placeholder={t('telegram_username_placeholder')}
                            defaultValue={''}
                            className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-base focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                            style={{
                              boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)',
                              fontSize: '16px'
                            }}
                            onChange={(e) => {
                              // Ensure the input starts with @
                              if (!e.target.value.startsWith('@') && e.target.value.length > 0) {
                                e.target.value = '@' + e.target.value;
                              }
                            }}
                          />
                        </div>
                        <button
                          className="bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          style={{
                            boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                          }}
                          onClick={() => initiateConnect('telegram')}
                          disabled={connectingPlatform === 'telegram'}
                        >
                          {connectingPlatform === 'telegram' ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              {t('connecting')}
                            </>
                          ) : (
                            t('connect')
                          )}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Web API */}
              {selectedPlatform === 'web' && (
                <div className="bg-deep-blue/80 border border-white/30 rounded-xl p-6 hover:border-white/40 transition-all">
                <div className="flex flex-col items-center">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-black/50 rounded-full flex items-center justify-center mr-4 text-purple-500 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                      <FaGlobe size={20} />
                    </div>
                    <div>
                      <h3 className="font-medium text-white text-lg font-title">{t('web_api')}</h3>
                      <p className="text-zinc-400 text-sm font-body">{t('coming_soon')}</p>
                    </div>
                  </div>
                  <p className="text-zinc-500 mt-2 font-body">{t('web_api_coming_soon')}</p>
                </div>
              </div>
              )}

              {/* Placeholder with improved styling */}
              {selectedPlatform === 'tiktok' && (
                <div className="bg-deep-blue/80 border border-white/20 rounded-xl p-6 hover:border-white/30 transition-all">
                <div className="flex flex-col items-center">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-black/50 rounded-full flex items-center justify-center mr-4 text-white shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                      <FaTiktok size={20} />
                    </div>
                    <div>
                      <h3 className="font-medium text-white text-lg font-title">{t('tiktok')}</h3>
                      <p className="text-zinc-400 text-sm font-body">{t('coming_soon')}</p>
                    </div>
                  </div>
                  <p className="text-zinc-500 mt-2 font-body">{t('more_platforms_coming_soon')}</p>
                </div>
              </div>
              )}
            </div>
            </div>
          </div>
          )}
        </div>
      </div>

      <Footer />
    </div>
  )
}
