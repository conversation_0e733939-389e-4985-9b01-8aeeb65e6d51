'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import Footer from '@/components/Footer'
import { FaBrain, FaListAlt, FaEdit, FaTrash, FaPlus, FaMinus, FaTimes } from 'react-icons/fa'
import imageCompression from 'browser-image-compression'
import { createClientComponentClient } from '@/utils/supabase/client'
import { getClientInfo } from '@/utils/client'
import { v4 as uuidv4 } from 'uuid'

// Product type definition
type Product = {
  id: string;
  prod_id: string;
  client_id: string;
  name: string;
  price: number;
  type: string;
  description: string;
  photo_urls?: string[]; // Array of photo URLs
  photo_file_paths?: string[]; // Array of file paths
}

export default function ProductListPage() {
  // Example state - similar to the main knowledge page
  const [businessInsightCount, setBusinessInsightCount] = useState(76)
  const [productInfoCount, setProductInfoCount] = useState(42)
  const [productListCount, setProductListCount] = useState(0)
  const [totalFaqs, setTotalFaqs] = useState<number | null>(null)
  const [isLoadingCount, setIsLoadingCount] = useState(true)
  const [isLoading, setIsLoading] = useState(true)
  const [userEmail, setUserEmail] = useState('')
  const [nextProductNumber, setNextProductNumber] = useState(1)

  // Add new state variables for subscription limits
  const [totalFaqsLimit, setTotalFaqsLimit] = useState<number | null>(null)
  const [productListLimit, setProductListLimit] = useState<number>(1)
  const [faqUsagePercentage, setFaqUsagePercentage] = useState(0)
  const [productUsagePercentage, setProductUsagePercentage] = useState(0)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 30

  // Status and confirmation states
  const [isUpdating, setIsUpdating] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)
  const confirmModalRef = useRef<HTMLDivElement>(null)
  const statusOverlayRef = useRef<HTMLDivElement>(null)

  // Create Supabase client
  const supabase = createClientComponentClient()

  // Product type management
  const [productTypes, setProductTypes] = useState<string[]>([])
  const [showAddTypeInput, setShowAddTypeInput] = useState(false) // Don't show by default
  const [newTypeValue, setNewTypeValue] = useState('')
  // Add state for error modal
  const [errorModalOpen, setErrorModalOpen] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const errorModalRef = useRef<HTMLDivElement>(null)

  // Product management state
  const [products, setProducts] = useState<Product[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [deleteConfirm, setDeleteConfirm] = useState<{prod_id: string, photo_file_path?: string | string[]} | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const deleteModalRef = useRef<HTMLDivElement>(null)
  const [formData, setFormData] = useState({
    name: '',
    price: '',
    type: '',
    description: '',
    imageFiles: [] as File[] // Changed from imageFile: File | null
  })
  // Add a unique ID for each image preview
  const [imagePreviews, setImagePreviews] = useState<Array<{id: string, url: string}>>([])
  const [compressedSizes, setCompressedSizes] = useState<number[]>([]) // Changed from compressedSize
  const [originalSizes, setOriginalSizes] = useState<number[]>([]) // Changed from originalSize
  const [isProcessingImage, setIsProcessingImage] = useState(false)
  const [uploadError, setUploadError] = useState('')

  // Update product form state
  const [showUpdateForm, setShowUpdateForm] = useState(false)
  const [updateFormData, setUpdateFormData] = useState<{
    prod_id: string,
    name: string,
    price: string,
    type: string,
    description: string,
    imageFiles: File[], // For newly uploaded files awaiting submission
    photo_urls?: string[], // Existing photo URLs from the database
    photo_file_paths?: string[] // File paths in storage
  }>({
    prod_id: '',
    name: '',
    price: '',
    type: '',
    description: '',
    imageFiles: [] // Initially empty
  })

  // Cancel confirmation modal state
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)
  const cancelConfirmRef = useRef<HTMLDivElement>(null)
  const [cancelAction, setCancelAction] = useState<'add' | 'update'>('add')

  // Refs for drag and drop
  const dropAreaRef = useRef<HTMLDivElement>(null)

  // Enable drag and drop for image upload
  useEffect(() => {
    const dropArea = dropAreaRef.current;
    if (!dropArea) return;

    const highlight = () => dropArea.classList.add('border-jade-purple', 'bg-zinc-800/70');
    const unhighlight = () => dropArea.classList.remove('border-jade-purple', 'bg-zinc-800/70');

    const preventDefaults = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDrop = async (e: DragEvent) => {
      unhighlight();
      preventDefaults(e);

      if (e.dataTransfer?.files && e.dataTransfer.files[0]) {
        await handleImageFile(e.dataTransfer.files[0]);
      }
    };

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      dropArea.addEventListener(eventName, preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
      dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      dropArea.addEventListener(eventName, unhighlight, false);
    });

    dropArea.addEventListener('drop', handleDrop, false);

    return () => {
      if (dropArea) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
          dropArea.removeEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
          dropArea.removeEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
          dropArea.removeEventListener(eventName, unhighlight, false);
        });

        dropArea.removeEventListener('drop', handleDrop, false);
      }
    };
  }, [formData.imageFiles]); // Add dependency to re-bind if files change

  // Function to check if WebP is supported
  const isWebpSupported = () => {
    const elem = document.createElement('canvas');
    if (elem.getContext && elem.getContext('2d')) {
      // was able or not to get WebP representation
      return elem.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }
    return false;
  };

  // Function to convert image to WebP
  const convertToWebP = (imageData: Blob): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      if (!isWebpSupported()) {
        // If WebP is not supported, return the original image
        resolve(imageData);
        return;
      }

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);

        // Convert to WebP if supported
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('WebP conversion failed'));
            }
          },
          'image/webp',
          0.8
        );
      };

      img.onerror = () => reject(new Error('Image loading failed'));
      img.src = URL.createObjectURL(imageData);
    });
  };

  // Validate file type and size
  const validateFile = (file: File): string | null => {
    // Check file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!validTypes.includes(file.type)) {
      return 'Only JPG and PNG files are accepted.';
    }

    // Check file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return 'File size must be less than 5MB.';
    }

    return null;
  };

  // Handle image compression and processing
  const processImage = async (file: File): Promise<{ compressedFile: File }> => {
    // Simplified compression options with consistent settings
    const options = {
      maxSizeMB: 1.0,
      maxWidthOrHeight: 1200,
      useWebWorker: true,
      fileType: 'image/jpeg', // Always JPEG
      initialQuality: 0.9,
      alwaysKeepResolution: true
    };

    try {
      console.log(`Starting compression for ${file.name} (${file.size} bytes, ${file.type})`);

      // Compress the image
      const compressedBlob = await imageCompression(file, options);
      console.log(`Compression complete: ${compressedBlob.size} bytes, ${compressedBlob.type}`);

      // Create a consistent filename with timestamp to avoid collisions
      const timestamp = Date.now();
      const baseFileName = file.name.split('.')[0].replace(/[^a-zA-Z0-9]/g, '_');
      const fileName = `${baseFileName}_${timestamp}.jpg`;

      // Create a proper File object from the blob
      const compressedFile = new File([compressedBlob], fileName, {
        type: 'image/jpeg',
        lastModified: Date.now()
      });

      console.log(`Final file created: ${compressedFile.name} (${compressedFile.size} bytes)`);
      return { compressedFile };

    } catch (error) {
      console.error('Image compression failed:', error);
      throw error;
    }
  };

  // Handle image file selection with a cleaner approach
  const handleImageFile = async (file: File) => {
    setUploadError('');

    // Check if maximum number of images reached
    if (formData.imageFiles.length >= 4) {
      setUploadError('Maximum of 4 images allowed.');
      return;
    }

    // Validate file
    const error = validateFile(file);
    if (error) {
      setUploadError(error);
      return;
    }

    try {
      setIsProcessingImage(true);

      // Step 1: Compress the image
      const { compressedFile } = await processImage(file);

      // Step 2: Create a preview from the compressed file
      const previewUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(compressedFile);
      });

      // Step 3: Update all state in a single batch to avoid race conditions
      // Using the same compressed file for both preview and later upload
      setFormData(prev => ({
        ...prev,
        imageFiles: [...prev.imageFiles, compressedFile]
      }));

      setImagePreviews(prev => [...prev, {
        id: `preview-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        url: previewUrl
      }]);
      setOriginalSizes(prev => [...prev, file.size]);
      setCompressedSizes(prev => [...prev, compressedFile.size]);

      console.log(`Image processed successfully: ${compressedFile.name}`);
      console.log(`Total images now: ${formData.imageFiles.length + 1}`);

    } catch (error) {
      console.error('Error processing image:', error);
      setUploadError('Failed to process image. Please try again.');
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Function to remove an image - Updated to delete from storage when needed
  const handleRemoveImage = async (index: number, formType: 'add' | 'update') => {
    // Log what we're trying to remove
    console.log(`Removing image at index ${index} from ${formType} form`);

    if (formType === 'add') {
      // For add form, we just need to remove from local state
      setFormData(prev => {
        console.log('Current form files:', prev.imageFiles.map(f => f.name));
        return {
          ...prev,
          imageFiles: prev.imageFiles.filter((_, i) => i !== index),
        };
      });
      setImagePreviews(prev => prev.filter((_, i) => i !== index));
      setCompressedSizes(prev => prev.filter((_, i) => i !== index));
      setOriginalSizes(prev => prev.filter((_, i) => i !== index));
    } else { // update form
      try {
        // Get current counts for clarity
        const existingImagesCount = updateFormData.photo_file_paths?.length || 0;
        const totalImagesCount = imagePreviews.length;

        console.log(`Remove image: index=${index}, existing=${existingImagesCount}, total=${totalImagesCount}, new=${updateFormData.imageFiles.length}`);

        // Update local state only, without removing from storage or database
        if (index < existingImagesCount) {
          // Existing image - just mark for removal in local state
          setUpdateFormData(prev => ({
            ...prev,
            photo_urls: prev.photo_urls?.filter((_, i) => i !== index),
            photo_file_paths: prev.photo_file_paths?.filter((_, i) => i !== index)
          }));
          console.log('Removed existing image from updateFormData');
        } else {
          // Calculate the correct index for newly added images
          // This is the position in the imageFiles array, not the visual position
          const newImageIndex = index - existingImagesCount;

          console.log(`Removing newly added image at file index ${newImageIndex}`);

          // New image - remove from imageFiles
          setUpdateFormData(prev => {
            const filesBefore = [...prev.imageFiles];
            const filesAfter = prev.imageFiles.filter((_, i) => i !== newImageIndex);
            console.log('Files before:', filesBefore.map(f => f.name));
            console.log('Files after:', filesAfter.map(f => f.name));
            return {
              ...prev,
              imageFiles: filesAfter
            };
          });
        }

        // Always update preview arrays
        setImagePreviews(prev => prev.filter((_, i) => i !== index));
        setCompressedSizes(prev => prev.filter((_, i) => i !== index));
        setOriginalSizes(prev => prev.filter((_, i) => i !== index));
      } catch (error) {
        console.error('Error removing image:', error);
      }
    }
  };

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('')
  const [sortBy, setSortBy] = useState('name')

  // Filtered products
  const filteredProducts = products
    .filter(product => {
      // Filter by search term (name or description)
      const matchesSearch = searchTerm === '' ||
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())

      // Filter by type
      const matchesType = filterType === '' || product.type === filterType

      return matchesSearch && matchesType
    })
    .sort((a, b) => {
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name)
      } else if (sortBy === 'price-low') {
        return a.price - b.price
      } else if (sortBy === 'price-high') {
        return b.price - a.price
      }
      return 0
    })

  // Calculate pagination
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  // Add a debug effect to log the photo URLs when products change
  useEffect(() => {
    if (paginatedProducts.length > 0) {
      console.log('Product images to be displayed:');
      paginatedProducts.forEach(product => {
        console.log({
          prod_id: product.prod_id,
          photo_urls: product.photo_urls,
          firstPhotoUrl: product.photo_urls && product.photo_urls.length > 0 ? product.photo_urls[0] : 'none'
        });
      });
    }
  }, [paginatedProducts]);

  // Update total pages when filtered products change
  useEffect(() => {
    setTotalPages(Math.max(1, Math.ceil(filteredProducts.length / itemsPerPage)))
    // Reset to first page when filters change
    if (currentPage > Math.max(1, Math.ceil(filteredProducts.length / itemsPerPage))) {
      setCurrentPage(1)
    }
  }, [filteredProducts, itemsPerPage])

  // Example function to handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Example function to handle image uploads - Updated for multiple files
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const currentCount = formData.imageFiles.length;
      const remainingSlots = 4 - currentCount;

      if (files.length > remainingSlots) {
        setUploadError(`You can only add ${remainingSlots} more image(s).`);
        // Optionally process only the allowed number of files
        files.slice(0, remainingSlots).forEach(file => handleImageFile(file));
      } else {
        files.forEach(file => handleImageFile(file));
      }
      // Reset the input value to allow selecting the same file again
      e.target.value = '';
    }
  };

  // Function to get email initials
  const getEmailInitials = (email: string): string => {
    if (!email) return 'XX';

    // Try to get the part before @ sign
    const username = email.split('@')[0] || '';

    // If username is at least 2 characters, use first 2 characters uppercase
    if (username.length >= 2) {
      return username.substring(0, 2).toUpperCase();
    }

    // If username is only 1 character, use it twice
    if (username.length === 1) {
      return (username + username).toUpperCase();
    }

    // Fallback
    return 'XX';
  }

  // Function to generate a product ID based on email initials and number from products_last_num table
  const generateProductId = async () => {
    try {
      // Get client ID
      const clientId = getClientInfo()?.client_id;
      if (!clientId) {
        throw new Error('Client ID not found');
      }

      // Get user session to verify authentication - needed for RLS
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session) {
        throw new Error('User session not found. Please log in again.');
      }

      // Get email initials (always use this method for initials)
      const initial = getEmailInitials(userEmail);

      // Check if client exists in products_last_num table
      const { data: lastNumData, error: lastNumError } = await supabase
        .from('products_last_num')
        .select('last_num')
        .eq('client_id', clientId)
        .single();

      let nextNum = 1;

      if (lastNumError || !lastNumData) {
        // If no record exists, start at 1
        console.log('No last_num record found for client, creating new one');

        // Create a new record in products_last_num
        const { error: insertError } = await supabase
          .from('products_last_num')
          .insert({
            client_id: clientId,
            last_num: 1
          });

        if (insertError) {
          console.error('Error creating products_last_num record:', insertError);
          // Continue anyway, using number 1
        }
      } else {
        // Use the last_num from the table and increment it
        nextNum = (lastNumData.last_num || 0) + 1;

        // Update the last_num in the table
        const { error: updateError } = await supabase
          .from('products_last_num')
          .update({ last_num: nextNum })
          .eq('client_id', clientId);

        if (updateError) {
          console.error('Error updating products_last_num:', updateError);
          // Continue anyway using the calculated number
        }
      }

      // Create sequential number with padding (e.g., 001, 002)
      const paddedNumber = nextNum.toString().padStart(3, '0');

      // Combine to create product ID
      const productId = `${initial}${paddedNumber}`;

      console.log('Generated product ID:', productId);

      return productId;
    } catch (error) {
      console.error('Error generating product ID:', error);
      // Return a fallback ID with timestamp (not ideal but prevents complete failure)
      return `XX${Date.now().toString().substring(7, 10)}`;
    }
  };

  // Handle click outside confirmation modal
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (confirmModalRef.current && !confirmModalRef.current.contains(event.target as Node)) {
        setShowConfirmation(false)
      }
    }

    if (showConfirmation) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showConfirmation])

  // Handle click outside delete confirmation modal
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // Only allow clicking outside to close the modal if we're not in the middle of deleting
      if (deleteModalRef.current && !deleteModalRef.current.contains(event.target as Node) && !isDeleting) {
        setDeleteConfirm(null)
      }
    }

    if (deleteConfirm) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [deleteConfirm, isDeleting])

  // Fetch products data from Supabase
  useEffect(() => {
    const fetchProductsData = async () => {
      try {
        setIsLoading(true)

        // Get client ID - still needed for UI purposes
        const clientId = getClientInfo()?.client_id
        if (!clientId) {
          console.error('Client ID not found')
          return
        }

        // Fetch user data to get email for product ID generation
        const { data: userData } = await supabase.auth.getUser()
        if (userData?.user?.email) {
          setUserEmail(userData.user.email)
        }

        // Fetch products from the database
        // With RLS, this will automatically filter to only show products
        // that the current auth user has access to based on the clients table relation
        const { data, error } = await supabase
          .from('products_list')
          .select('*')
          // No need to filter by client_id as RLS will handle that
          // .eq('client_id', clientId)

        if (error) {
          console.error('Error fetching products:', error)
          return
        }

        // Make sure we only work with products that match our clientId from session
        // This is a safety check in case RLS returns products for multiple clients
        const clientProducts = data?.filter(product => product.client_id === clientId) || []

        // Debug log to see what we're getting from the database
        console.log('Products from database:', clientProducts.map(p => ({
          id: p.id,
          prod_id: p.prod_id,
          photo_url: p.photo_url, // This is what should be displayed
        })))

        // Extract unique product types
        const types = Array.from(new Set(clientProducts?.map(item => item.type) || []))
          .filter(Boolean) as string[]

        setProductTypes(types)

        // Only show add type input if no types exist
        setShowAddTypeInput(types.length === 0)

        // Find the highest product number to set next product number
        if (clientProducts && clientProducts.length > 0) {
          // Extract numbers from product IDs that match our pattern
          const productNumbers = clientProducts
            .map(item => {
              const match = item.prod_id.match(/[A-Z]{1,2}(\d{3})/)
              return match ? parseInt(match[1], 10) : 0
            })
            .filter(num => !isNaN(num))

          const highestNumber = Math.max(0, ...productNumbers)
          setNextProductNumber(highestNumber + 1)

          // Transform data to our Product type
          const transformedProducts = clientProducts.map(item => {
            // Make sure we handle the photo URLs correctly
            const photoUrls = item.photo_url || [];

            return {
              id: item.id || uuidv4(),
              prod_id: item.prod_id,
              client_id: item.client_id,
              name: item.name,
              price: parseFloat(item.price),
              type: item.type,
              description: item.description,
              photo_urls: photoUrls, // Store the original URLs from DB
              photo_file_paths: item.photo_file_path || [] // Store the original paths
            };
          });

          // Debug log the transformed products to ensure URLs are correct
          console.log('Transformed products for display:', transformedProducts.map(p => ({
            prod_id: p.prod_id,
            photo_urls: p.photo_urls
          })));

          setProducts(transformedProducts)
          setProductListCount(transformedProducts.length)

          // Update subscription limits after setting product count
          fetchFaqCount();
        } else {
          // No products yet, start with 1
          setNextProductNumber(1)

          // Still fetch limits even if there are no products
          fetchFaqCount();
        }
      } catch (error) {
        console.error('Error in fetch products:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchProductsData()
  }, [supabase])

  // Fetch FAQ count
  const fetchFaqCount = async () => {
    try {
      const clientId = getClientInfo()?.client_id;

      if (!clientId) {
        console.error('Client ID not found while fetching FAQ count');
        setTotalFaqs(0);
        return 0;
      }

      const { count, error } = await supabase
        .from('faqs')
        .select('client_id', { count: 'exact', head: true })
        .eq('client_id', clientId);

      if (error) {
        console.error('Error fetching FAQ count:', error);
        return 0;
      }

      const faqCount = count || 0;
      setTotalFaqs(faqCount);
      return faqCount;
    } catch (error) {
      console.error('Error in fetchFaqCount:', error);
      return 0;
    }
  };

  // Add fetchSubscriptionLimits function - get plan limits
  const fetchSubscriptionLimits = async () => {
    try {
      const clientInfo = getClientInfo();
      const clientId = clientInfo?.client_id;
      const planType = clientInfo?.subscription_tier;

      if (!clientId) {
        console.error('Client ID not found while fetching subscription limits');
        return;
      }

      if (!planType) {
        console.error('No subscription plan found in client info');
        return;
      }

      // Get all plans and filter in JavaScript instead of using eq()
      const { data: allPlans, error: planError } = await supabase
        .from('plans')
        .select('name, total_faqs, total_products');

      if (planError) {
        console.error('Error fetching plans data:', planError);
        return;
      }

      // Find the matching plan with case-insensitive comparison
      const planData = allPlans?.find(plan =>
        plan.name.trim().toLowerCase() === planType.trim().toLowerCase()
      );

      if (!planData) {
        console.error('No matching plan found for:', planType);
        // Set default values to 0 to make it clear there's an issue
        setTotalFaqsLimit(0);
        setProductListLimit(1);
        return;
      }

      // Set the limits
      setTotalFaqsLimit(planData.total_faqs || 0);
      setProductListLimit(planData.total_products || 1);

      // Calculate usage percentages
      if (planData.total_faqs > 0 && totalFaqs !== null) {
        const faqPercentage = (totalFaqs / planData.total_faqs) * 100;
        setFaqUsagePercentage(Math.min(faqPercentage, 100));
      }

      // Calculate product usage percentage based on current count
      if (planData.total_products > 0) {
        const productPercentage = (productListCount / planData.total_products) * 100;
        setProductUsagePercentage(Math.min(productPercentage, 100));
      }

    } catch (error) {
      console.error('Error in fetchSubscriptionLimits:', error);
    }
  }

  // Show confirmation before submit
  const handleSubmitConfirm = (e: React.FormEvent) => {
    e.preventDefault()

    // No validation needed - all fields are optional now

    // Show confirmation modal
    setShowConfirmation(true)
  }

  // Function to handle form submission
  const handleSubmit = async () => {
    try {
      setIsUpdating(true);
      setUpdateStatus('loading');
      setUpdateMessage('Adding product...');
      setShowConfirmation(false); // Close confirmation modal immediately

      // Get session to verify authentication
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session?.user?.id) {
        throw new Error('User session not found or expired. Please log in again.');
      }

      // Get client ID
      const clientId = getClientInfo()?.client_id
      if (!clientId) {
        throw new Error('Client ID not found. Please ensure you are properly logged in.')
      }

      // IMPORTANT: Verify that the client_id belongs to the current authenticated user
      // This is required for the RLS policy to allow the operation
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('client_id')
        .eq('client_id', clientId)
        .eq('auth_id', session.user.id)
        .single();

      if (clientError || !clientData) {
        console.error('Client verification error:', clientError);
        throw new Error('You do not have permission to add products for this client. Please contact support.');
      }

      // Generate product ID
      const prod_id = await generateProductId()

      // Upload images to Supabase Storage if available
      let photo_urls = [] as string[]
      let photo_file_paths = [] as string[]

      if (formData.imageFiles.length > 0) {
        try {
          setUpdateMessage('Uploading product images...')
          setUpdateProgress(20)

          const userId = session.user.id;

          // Log what files we're about to upload - these should be the compressed files
          console.log(`Uploading ${formData.imageFiles.length} images for product ${prod_id}`);

          // Upload each file - these are already compressed JPEG files from our state
          for (let i = 0; i < formData.imageFiles.length; i++) {
            const imageFile = formData.imageFiles[i];

            // Generate a unique ID using UUID instead of sequence number
            const uniqueId = uuidv4();
            const uniqueFileName = `${prod_id}-${uniqueId}.jpg`;
            const filePath = `${userId}/${uniqueFileName}`;

            console.log(`Uploading ${imageFile.name} to ${filePath}`);

            // Upload the file exactly as is from our state
            const { data: uploadData, error: uploadError } = await supabase
              .storage
              .from('product-images')
              .upload(filePath, imageFile, {
                cacheControl: '3600',
                upsert: true
              });

            if (uploadError) {
              console.error('Upload error:', uploadError);
              throw new Error(`Failed to upload image: ${uploadError.message}`);
            }

            // Get the public URL
            const { data: urlData } = supabase
              .storage
              .from('product-images')
              .getPublicUrl(filePath);

            if (!urlData?.publicUrl) {
              throw new Error('Failed to get URL for uploaded image');
            }

            // Store the URL and path
            photo_urls.push(urlData.publicUrl);
            photo_file_paths.push(filePath);

            setUpdateProgress(40 + (i * 15));
          }

          // Log the final URLs that will be saved to the database
          console.log('Final image URLs to save:', photo_urls);

        } catch (error) {
          console.error('Image upload error:', error);
          throw error;
        }
      }

      setUpdateMessage('Saving product data...')
      setUpdateProgress(80)

      // Insert product data into the database
      const { data, error } = await supabase
        .from('products_list')
        .insert({
          prod_id: prod_id,
          client_id: clientId,
          name: formData.name,
          price: parseFloat(formData.price) || 0,
          type: formData.type,
          description: formData.description,
          photo_url: photo_urls,
          photo_file_path: photo_file_paths
        })
        .select()

      if (error) {
        throw error
      }

      setUpdateProgress(100)

      // Add the new product to the list using data directly from database response
      setProducts(prev => [...prev, {
        id: data[0].id,
        prod_id,
        client_id: clientId,
        name: formData.name,
        price: parseFloat(formData.price) || 0,
        type: formData.type,
        description: formData.description,
        photo_urls: photo_urls,
        photo_file_paths: photo_file_paths
      }]);

      // Reset the form and state
      resetAddForm();
      setShowAddForm(false);

      // Update product count
      setProductListCount(prev => prev + 1); // Simple increment

      // Recalculate usage percentage
      fetchFaqCount(); // Refresh stats after adding

      // Show success message
      setUpdateStatus('success')
      setUpdateMessage(`Product "${formData.name}" has been added successfully!`)

      // Auto-dismiss after delay
      setTimeout(() => {
        setUpdateStatus('idle')
        setIsUpdating(false)
      }, 2000)

    } catch (error: any) {
      console.error('Error creating product:', error)
      setUpdateStatus('error')
      setUpdateMessage(error.message || 'Failed to create product. Please try again.')
      setIsUpdating(false)
      setShowConfirmation(false) // Ensure modal is closed on error too
    }
  }

  // Show update confirmation
  const handleUpdateSubmitConfirm = (e: React.FormEvent) => {
    e.preventDefault()

    // No validation needed - all fields are optional now

    // Show confirmation modal
    setShowConfirmation(true)
  }

  // Handle product update
  const handleUpdateSubmit = async () => {
    try {
      setIsUpdating(true)
      setUpdateStatus('loading')
      setUpdateMessage('Updating product...')
      setShowConfirmation(false) // Close confirmation modal immediately

      // Get session to verify authentication
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session?.user?.id) {
        throw new Error('User session not found or expired. Please log in again.');
      }

      // Get client ID
      const clientId = getClientInfo()?.client_id
      if (!clientId) {
        throw new Error('Client ID not found. Please ensure you are properly logged in.')
      }

      // IMPORTANT: Verify that the client_id belongs to the current authenticated user
      // This is required for the RLS policy to allow the operation
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('client_id')
        .eq('client_id', clientId)
        .eq('auth_id', session.user.id)
        .single();

      if (clientError || !clientData) {
        console.error('Client verification error:', clientError);
        throw new Error('You do not have permission to update products for this client. Please contact support.');
      }

      // First, fetch the current product to get the original paths
      const { data: currentProduct, error: fetchError } = await supabase
        .from('products_list')
        .select('photo_file_path')
        .eq('prod_id', updateFormData.prod_id)
        .eq('client_id', clientId)
        .single();

      if (fetchError) {
        console.error('Error fetching product data:', fetchError);
        throw new Error('Failed to retrieve current product data.');
      }

      // Find images that need to be deleted from storage
      const currentPaths = Array.isArray(currentProduct.photo_file_path) ?
        currentProduct.photo_file_path :
        (currentProduct.photo_file_path ? [currentProduct.photo_file_path] : []);

      const updatedPaths = updateFormData.photo_file_paths || [];

      // Find paths that are in the original but not in the updated list
      const pathsToDelete = currentPaths.filter(path => !updatedPaths.includes(path));

      // Delete removed images from storage
      if (pathsToDelete.length > 0) {
        setUpdateMessage('Removing deleted images...')
        setUpdateProgress(10)

        for (const pathToDelete of pathsToDelete) {
          console.log(`Deleting image from storage: ${pathToDelete}`);
          const { error: deleteError } = await supabase
            .storage
            .from('product-images')
            .remove([pathToDelete]);

          if (deleteError) {
            console.error('Error deleting image from storage:', deleteError);
            // Continue anyway
          }
        }
      }

      // Start with existing URLs and paths
      let photo_urls = updateFormData.photo_urls || [];
      let photo_file_paths = updateFormData.photo_file_paths || [];

      console.log('Before upload - Current URLs:', photo_urls);
      console.log('Before upload - Current paths:', photo_file_paths);
      console.log('New images to upload:', updateFormData.imageFiles.length);

      // Process any new images
      if (updateFormData.imageFiles.length > 0) {
        try {
          setUpdateMessage('Uploading new product images...')
          setUpdateProgress(20)

          // User ID already verified above
          const userId = session.user.id;

          // Debug log showing we're using the SAME compressed files from form state
          console.log('Update form - Files to upload from form state:', updateFormData.imageFiles.map(f =>
            `${f.name} (${f.size} bytes, ${f.type})`));

          // Process the new images - files should already be compressed
          for (let i = 0; i < updateFormData.imageFiles.length; i++) {
            const fileToUpload = updateFormData.imageFiles[i];
            console.log(`Uploading update file ${i+1}/${updateFormData.imageFiles.length}: ${fileToUpload.name}, size: ${fileToUpload.size}, type: ${fileToUpload.type}`);

            // Verify this is a JPEG file as expected from our compression step
            if (fileToUpload.type !== 'image/jpeg') {
              console.warn(`Warning: File ${fileToUpload.name} is not a JPEG (${fileToUpload.type}). This may indicate an issue in the compression process.`);
            }

            // Use jpg extension for compressed files
            const fileExt = 'jpg';

            // Generate a unique ID using UUID instead of sequence number
            const uniqueId = uuidv4();
            const uniqueFileName = `${updateFormData.prod_id}-${uniqueId}.${fileExt}`;
            const filePath = `${userId}/${uniqueFileName}`;

            console.log(`Uploading update image ${i+1}/${updateFormData.imageFiles.length}: ${filePath}`);

            try {
              // Upload directly to storage bucket
              const { error: uploadError } = await supabase
                .storage
                .from('product-images')
                .upload(filePath, fileToUpload, {
                  cacheControl: '3600',
                  upsert: true
                });

              if (uploadError) {
                console.error(`File upload error for ${fileToUpload.name}:`, uploadError);
                console.error('File upload path:', filePath);
                throw uploadError;
              }

              // Get public URL
              const { data: urlData } = supabase
                .storage
                .from('product-images')
                .getPublicUrl(filePath);

              if (!urlData?.publicUrl) {
                throw new Error('Failed to get URL for uploaded image.');
              }

              // Add to arrays
              photo_urls.push(urlData.publicUrl);
              photo_file_paths.push(filePath);

              console.log(`Successfully uploaded update image ${i+1}`);
              setUpdateProgress(40 + (i * 10));
            } catch (singleUploadError: any) {
              console.error(`Error uploading update file ${i+1}:`, singleUploadError);
              throw new Error(`Failed to upload image: ${singleUploadError.message || 'Unknown error'}`);
            }
          }

          console.log('Successfully uploaded all new update images');

        } catch (uploadError) {
          console.error('Error processing update image upload:', uploadError);
          throw uploadError;
        }
      }

      setUpdateMessage('Saving product data...')
      setUpdateProgress(80)

      // Update product data in the database
      // RLS will verify the user has access to this product
      const { data, error } = await supabase
        .from('products_list')
        .update({
          name: updateFormData.name,
          price: parseFloat(updateFormData.price) || 0,
          type: updateFormData.type,
          description: updateFormData.description,
          photo_url: photo_urls,
          photo_file_path: photo_file_paths
        })
        .eq('prod_id', updateFormData.prod_id)
        .eq('client_id', clientId)
        .select()

      if (error) {
        console.error("Database update error:", error);
        throw error
      }

      setUpdateProgress(100)

      // Update the product in the local state
      setProducts(prev => prev.map(product =>
        product.prod_id === updateFormData.prod_id
          ? {
              ...product,
              name: updateFormData.name,
              price: parseFloat(updateFormData.price) || 0,
              type: updateFormData.type,
              description: updateFormData.description,
              photo_urls: photo_urls,
              photo_file_paths: photo_file_paths
            }
          : product
      ))

      // Reset form and close it
      setUpdateFormData({
        prod_id: '',
        name: '',
        price: '',
        type: '',
        description: '',
        imageFiles: []
      })
      setImagePreviews([])
      setCompressedSizes([])
      setOriginalSizes([])
      setShowUpdateForm(false)

      // Show success message
      setUpdateStatus('success')
      setUpdateMessage(`Product "${updateFormData.name}" has been updated successfully!`)

      // Auto-dismiss after delay
      setTimeout(() => {
        setUpdateStatus('idle')
        setIsUpdating(false)
      }, 1500)

      // After successful update
      const { totalProducts } = await fetchProductCount();
      setProductListCount(totalProducts);

      // Recalculate usage percentage
      if (productListLimit > 0) {
        setProductUsagePercentage(Math.round((totalProducts / productListLimit) * 100));
      }

    } catch (error: any) {
      console.error('Error updating product:', error)
      setUpdateStatus('error')
      setUpdateMessage(error.message || 'Failed to update product. Please try again.')
      setIsUpdating(false)
      setShowConfirmation(false) // Ensure modal is closed on error too
    }
  }

  // Cancel form - immediately reset without confirmation
  const handleCancelForm = () => {
    // Always reset and close immediately without confirmation
    resetAddForm()
  }

  // Cancel update form - also immediately reset without confirmation
  const handleCancelUpdateForm = () => {
    // Always reset and close immediately without confirmation
    resetUpdateForm()
  }

  // Reset add form - ensure ALL state is properly cleared
  const resetAddForm = () => {
    console.log("Resetting add form - clearing ALL state");

    // Reset main form data
    setFormData({
      name: '',
      price: '',
      type: '',
      description: '',
      imageFiles: []
    });

    // Reset all image-related state
    setImagePreviews([]);
    setCompressedSizes([]);
    setOriginalSizes([]);
    setUploadError('');
    setIsProcessingImage(false);

    // Finally, hide the form
    setShowAddForm(false);
  };

  // Reset update form
  const resetUpdateForm = () => {
    console.log("Resetting update form - clearing all state");
    setShowUpdateForm(false);
    setUpdateFormData({
      prod_id: '',
      name: '',
      price: '',
      type: '',
      description: '',
      imageFiles: []
    });
    setImagePreviews([]);
    setCompressedSizes([]);
    setOriginalSizes([]);
    setUploadError('');
  };

  // Confirm cancel action
  const confirmCancel = () => {
    if (cancelAction === 'add') {
      resetAddForm()
    } else {
      resetUpdateForm()
    }
    setShowCancelConfirm(false)
  }

  // Function to handle adding a new product type
  const handleAddProductType = (e: React.FormEvent) => {
    e.preventDefault()

    if (!newTypeValue.trim()) return

    // Add the new type to state
    const updatedTypes = [...productTypes, newTypeValue]
    setProductTypes(updatedTypes)

    // Reset input and hide
    setNewTypeValue('')
    setShowAddTypeInput(false)

    // Set form data to use the new type
    if (showAddForm) {
      setFormData(prev => ({
        ...prev,
        type: newTypeValue
      }))
    } else if (showUpdateForm) {
      setUpdateFormData(prev => ({
        ...prev,
        type: newTypeValue
      }))
    }
  }

  // Show delete confirmation
  const showDeleteConfirmation = (prod_id: string, photo_file_paths?: string | string[]) => {
    setDeleteConfirm({ prod_id, photo_file_path: photo_file_paths })
  }

  // Perform the actual delete operation
  const handleDeleteFromDatabase = async () => {
    if (!deleteConfirm) return;

    try {
      setIsDeleting(true);

      // Get session to verify authentication
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session?.user?.id) {
        throw new Error('User session not found or expired. Please log in again.');
      }

      // Get client ID for validation
      const clientId = getClientInfo()?.client_id
      if (!clientId) {
        console.error('Client ID not found')
        return
      }

      // IMPORTANT: Verify that the client_id belongs to the current authenticated user
      // This is required for the RLS policy to allow the operation
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('client_id')
        .eq('client_id', clientId)
        .eq('auth_id', session.user.id)
        .single();

      if (clientError || !clientData) {
        console.error('Client verification error:', clientError);
        throw new Error('You do not have permission to delete products for this client. Please contact support.');
      }

      // Get the product to delete to access its file paths
      const productToDelete = products.find(p => p.prod_id === deleteConfirm.prod_id)

      // Delete from database - RLS will verify the user has access to this product
      const { error } = await supabase
        .from('products_list')
        .delete()
        .eq('prod_id', deleteConfirm.prod_id)
        .eq('client_id', clientId)

      if (error) {
        console.error('Error deleting product:', error)
        throw error
      }

      // Delete images from storage if they exist
      // Note: Storage access isn't governed by the RLS policy mentioned,
      // but depends on Storage bucket policies
      if (deleteConfirm.photo_file_path || (productToDelete && productToDelete.photo_file_paths)) {
        // Get file paths either from deleteConfirm or from the product object
        const filePaths = deleteConfirm.photo_file_path ?
          (Array.isArray(deleteConfirm.photo_file_path) ? deleteConfirm.photo_file_path : [deleteConfirm.photo_file_path]) :
          (productToDelete && productToDelete.photo_file_paths ?
           (Array.isArray(productToDelete.photo_file_paths) ? productToDelete.photo_file_paths : [productToDelete.photo_file_paths]) :
           []);

        for (const path of filePaths) {
          if (path) {
            const { error: storageError } = await supabase
              .storage
              .from('product-images')
              .remove([path])

            if (storageError) {
              console.error(`Error deleting image at path ${path}:`, storageError)
            }
          }
        }
      }

      // Update UI
      setProducts(products.filter(item => item.prod_id !== deleteConfirm.prod_id))
      setProductListCount(prev => prev - 1)

      // Close modal and reset states
      setDeleteConfirm(null)
      setIsDeleting(false)

      // After successful deletion
      fetchFaqCount(); // Refresh stats after deleting

      // Recalculate usage percentage
      // Usage percentage updated within fetchFaqCount -> fetchSubscriptionLimits

    } catch (error) {
      console.error('Failed to delete product:', error)
      setIsDeleting(false)

      // Show error to user
      setUpdateStatus('error')
      setUpdateMessage(error instanceof Error ? error.message : 'Failed to delete product')
      setTimeout(() => {
        setUpdateStatus('idle')
      }, 3000)
    }
  }

  // Handle delete function
  const handleDeleteProduct = (prod_id: string, filePathsToDelete?: string | string[]) => {
    showDeleteConfirmation(prod_id, filePathsToDelete)
  }

  // Show edit form
  const handleEditProduct = (product: Product) => {
    // Hide add form if it's open
    setShowAddForm(false)

    // Set the update form data
    setUpdateFormData({
      prod_id: product.prod_id,
      name: product.name,
      price: product.price.toString(),
      type: product.type,
      description: product.description,
      imageFiles: [],
      photo_urls: product.photo_urls || [],
      photo_file_paths: product.photo_file_paths || []
    })

    // Show the update form
    setShowUpdateForm(true)

    // Reset the image previews
    if (product.photo_urls && product.photo_urls.length > 0) {
      setImagePreviews(product.photo_urls.map(url => ({ id: `preview-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, url })))
    } else {
      setImagePreviews([])
    }
    setCompressedSizes([])
    setOriginalSizes([])
  }

  // Handle click outside cancel confirm modal
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (cancelConfirmRef.current && !cancelConfirmRef.current.contains(event.target as Node)) {
        setShowCancelConfirm(false)
      }
    }

    if (showCancelConfirm) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showCancelConfirm])

  // Handle pagination
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
    }
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const handlePageClick = (page: number) => {
    setCurrentPage(page)
  }

  // Generate pagination buttons
  const renderPaginationButtons = () => {
    const buttons = []
    const maxVisibleButtons = 5

    let startPage, endPage

    if (totalPages <= maxVisibleButtons) {
      startPage = 1
      endPage = totalPages
    } else {
      const halfWay = Math.floor(maxVisibleButtons / 2)

      if (currentPage <= halfWay + 1) {
        startPage = 1
        endPage = maxVisibleButtons
      } else if (currentPage >= totalPages - halfWay) {
        startPage = totalPages - maxVisibleButtons + 1
        endPage = totalPages
      } else {
        startPage = currentPage - halfWay
        endPage = currentPage + halfWay
      }
    }

    // Add first page button if not visible
    if (startPage > 1) {
      buttons.push(
        <button
          key="first"
          onClick={() => handlePageClick(1)}
          className="px-3 py-1 rounded bg-zinc-800 text-zinc-300 hover:bg-zinc-700 text-[10px] sm:text-xs"
        >
          1
        </button>
      )
      if (startPage > 2) {
        buttons.push(<span key="dots1" className="px-2 text-zinc-500">...</span>)
      }
    }

    // Add page buttons
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => handlePageClick(i)}
          className={`px-3 py-1 rounded text-[10px] sm:text-xs ${currentPage === i ? 'bg-jade-purple text-white' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`}
        >
          {i}
        </button>
      )
    }

    // Add last page button if not visible
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        buttons.push(<span key="dots2" className="px-2 text-zinc-500">...</span>)
      }
      buttons.push(
        <button
          key="last"
          onClick={() => handlePageClick(totalPages)}
          className="px-3 py-1 rounded bg-zinc-800 text-zinc-300 hover:bg-zinc-700 text-[10px] sm:text-xs"
        >
          {totalPages}
        </button>
      )
    }

    return buttons
  }

  // Function to handle update form image changes
  const handleUpdateImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const currentCount = updateFormData.imageFiles.length + (updateFormData.photo_urls?.length || 0);
      const remainingSlots = 4 - currentCount;

      if (files.length > remainingSlots) {
        setUploadError(`You can only add ${remainingSlots} more image(s).`);
        // Process only the allowed number of files
        files.slice(0, remainingSlots).forEach(file => handleUpdateImageFile(file));
      } else {
        files.forEach(file => handleUpdateImageFile(file));
      }
      // Reset the input value
      e.target.value = '';
    }
  };

  // Handler specifically for update form images
  const handleUpdateImageFile = async (file: File) => {
    setUploadError('');

    // Validate file
    const error = validateFile(file);
    if (error) {
      setUploadError(error);
      return;
    }

    try {
      setIsProcessingImage(true);
      console.log(`Processing image for UPDATE FORM: ${file.name}, size: ${file.size}, type: ${file.type}`);

      // Process the image (compress to JPEG) FIRST
      const { compressedFile } = await processImage(file);
      console.log(`Compressed file created for update: ${compressedFile.name}, size: ${compressedFile.size}, type: ${compressedFile.type}`);

      // Then create preview from the COMPRESSED file (not the original)
      const reader = new FileReader();
      reader.onloadend = () => {
        const previewUrl = reader.result as string;
        console.log(`Preview created for update form: ${compressedFile.name}`);

        // Create an actual DOM image element to verify the preview
        const img = new Image();
        img.onload = () => {
          console.log(`Preview image loaded successfully: ${img.width}x${img.height}`);

          // IMPORTANT: Add the compressed file to the form state FIRST
          // This ensures the file that will be uploaded is the same one we're previewing
          const newCompressedFile = compressedFile; // Create reference to avoid any potential issues

          // Add all state updates together to ensure consistency
          setUpdateFormData(prev => {
            const newFiles = [...prev.imageFiles, newCompressedFile];
            console.log(`Update form data updated with new file: ${newFiles.map(f => f.name).join(', ')}`);
            return {
              ...prev,
              imageFiles: newFiles
            };
          });

          setImagePreviews(prev => {
            const newPreviews = [...prev, {
              id: `preview-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              url: previewUrl
            }];
            console.log(`Update form image previews updated: ${newPreviews.length} total`);
            return newPreviews;
          });

          setOriginalSizes(prev => [...prev, file.size]);
          setCompressedSizes(prev => [...prev, newCompressedFile.size]);
        };

        img.onerror = (e) => {
          console.error('Error loading preview image:', e);
          setUploadError('There was a problem generating the image preview. Please try another image.');
        };

        img.src = previewUrl;
      };
      reader.readAsDataURL(compressedFile); // Read the compressed file, not original

    } catch (error) {
      console.error('Error processing image for update:', error);
      setUploadError('Failed to process image. Please try again.');
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Add state for image preview modal and current image index
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [previewImages, setPreviewImages] = useState<string[]>([])
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isZoomed, setIsZoomed] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Function to open image preview with all available images
  const openImagePreview = (images: string[], initialIndex: number = 0) => {
    console.log('Opening preview with images:', {
      imageCount: images.length,
      initialIndex,
      firstImageUrl: images[0]?.substring(0, 50) + '...',
      allImages: images
    });

    // Ensure we're working with valid images
    const validImages = images.filter(url => url && url.trim() !== '');

    if (validImages.length === 0) {
      console.error('No valid images found to preview');
      return;
    }

    if (initialIndex >= validImages.length) {
      console.warn(`Initial index ${initialIndex} is out of bounds, resetting to 0`);
      initialIndex = 0;
    }

    setPreviewImages(validImages);
    setCurrentImageIndex(initialIndex);
    setPreviewImage(validImages[initialIndex]);
    setIsZoomed(false); // Reset zoom state when opening

    console.log('Image preview set to:', {
      totalValidImages: validImages.length,
      currentIndex: initialIndex,
      currentImage: validImages[initialIndex]?.substring(0, 50) + '...'
    });

    // Pre-load all images to avoid flashing during navigation
    validImages.forEach((url, i) => {
      const img = new Image();
      img.src = url;
      img.onload = () => console.log(`Preloaded image ${i+1}/${validImages.length}: ${img.width}x${img.height}`);
      img.onerror = () => console.error(`Failed to preload image ${i+1}/${validImages.length}`);
    });
  }

  // Navigate to previous image
  const showPreviousImage = () => {
    if (previewImages.length <= 1) return;
    const newIndex = (currentImageIndex - 1 + previewImages.length) % previewImages.length;
    setCurrentImageIndex(newIndex);
    setPreviewImage(previewImages[newIndex]);
    setIsZoomed(false); // Reset zoom state
  }

  // Navigate to next image
  const showNextImage = () => {
    if (previewImages.length <= 1) return;
    const newIndex = (currentImageIndex + 1) % previewImages.length;
    setCurrentImageIndex(newIndex);
    setPreviewImage(previewImages[newIndex]);
    setIsZoomed(false); // Reset zoom state
  }

  // Button to toggle the add form with better state management
  const toggleAddForm = () => {
    if (showAddForm) {
      // If currently shown, check if we need to confirm
      handleCancelForm();
    } else {
      // If Update form is open, close it first
      if (showUpdateForm) {
        resetUpdateForm();
      }

      // If currently hidden, make sure to reset everything first
      // This ensures no stale data from previous attempts
      setFormData({
        name: '',
        price: '',
        type: '',
        description: '',
        imageFiles: []
      });

      setImagePreviews([]);
      setCompressedSizes([]);
      setOriginalSizes([]);
      setUploadError('');
      setIsProcessingImage(false);

      // Now show the form
      setShowAddForm(true);
    }
  };

  // Add function to handle product type deletion - if it doesn't exist already
  const handleDeleteProductType = (typeToDelete: string) => {
    // Check if type is in use by any products
    const typeInUse = products.some(product => product.type === typeToDelete);

    if (typeInUse) {
      // Show custom error modal instead of alert
      setErrorMessage(`Cannot delete "${typeToDelete}" because it is currently in use by one or more products.`)
      setErrorModalOpen(true)
      return
    }
    setProductTypes(prev => prev.filter(type => type !== typeToDelete))
  }

  // Add click outside handler for error modal
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (errorModalRef.current && !errorModalRef.current.contains(event.target as Node)) {
        setErrorModalOpen(false);
      }
    }

    if (errorModalOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [errorModalOpen]);

  // Handle touch start
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  // Handle touch move
  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  // Handle touch end
  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !previewImage) return;

    // If zoomed, don't allow swiping
    if (isZoomed) return;

    const minSwipeDistance = 50; // Minimum distance in pixels to be considered a swipe
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      // Handle left swipe (next image)
      showNextImage();
    }

    if (isRightSwipe) {
      // Handle right swipe (previous image)
      showPreviousImage();
    }

    // Reset touch coordinates
    setTouchStart(null);
    setTouchEnd(null);
  };

  // Handle image click for zoom toggle
  const handleImageClick = () => {
    if (previewImage) {
      setIsZoomed(!isZoomed);
    }
  };

  // Close image gallery when clicking outside or pressing escape
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (imageGalleryRef.current && !imageGalleryRef.current.contains(event.target as Node)) {
        setPreviewImage(null);
      }
    }

    function handleKeyDown(event: KeyboardEvent) {
      if (!previewImage) return;

      if (event.key === 'Escape') {
        if (isZoomed) {
          // If zoomed, first reset zoom rather than closing the gallery
          setIsZoomed(false);
        } else {
          // If not zoomed, then close the gallery
          setPreviewImage(null);
        }
      } else if (event.key === 'ArrowLeft') {
        showPreviousImage();
      } else if (event.key === 'ArrowRight') {
        showNextImage();
      }
    }

    if (previewImage) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    }
  }, [previewImage, isZoomed, currentImageIndex, previewImages]);

  // Function to ensure image URLs are valid
  const getValidImageUrl = (urls?: string[] | null): string => {
    if (!urls || urls.length === 0) {
      return '/images/placeholder-product.jpg'; // Use a placeholder image
    }

    // Find the first valid URL
    const validUrl = urls.find(url => url && url.trim() !== '');
    return validUrl || '/images/placeholder-product.jpg';
  };

  // Fetch products for the current page
  const fetchProductsData = async () => {
    try {
      const clientId = getClientInfo()?.client_id;
      if (!clientId) {
        console.error('Client ID not found');
        return;
      }

      const { data: userData } = await supabase.auth.getUser();
      if (userData?.user?.email) {
        setUserEmail(userData.user.email);
      }

      const { data, error } = await supabase
        .from('products_list')
        .select('*');

      if (error) {
        console.error('Error fetching products:', error);
        return;
      }

      const clientProducts = data?.filter(product => product.client_id === clientId) || [];
      const types = Array.from(new Set(clientProducts?.map(item => item.type) || []))
        .filter(Boolean) as string[];

      setProductTypes(types);
      setShowAddTypeInput(types.length === 0);

      if (clientProducts.length > 0) {
        const productNumbers = clientProducts
          .map(item => {
            const match = item.prod_id.match(/[A-Z]{1,2}(\d{3})/);
            return match ? parseInt(match[1], 10) : 0;
          })
          .filter(num => !isNaN(num));

        const highestNumber = Math.max(0, ...productNumbers);
        setNextProductNumber(highestNumber + 1);

        const transformedProducts = clientProducts.map(item => ({
          id: item.id || uuidv4(),
          prod_id: item.prod_id,
          client_id: item.client_id,
          name: item.name,
          price: parseFloat(item.price),
          type: item.type,
          description: item.description,
          photo_urls: item.photo_url || [],
          photo_file_paths: item.photo_file_path || []
        }));

        setProducts(transformedProducts);
        setProductListCount(transformedProducts.length);
      } else {
        setNextProductNumber(1);
      }
    } catch (error) {
      console.error('Error in fetch products:', error);
      throw error;
    }
  };

  // Load initial data
  useEffect(() => {
    const initializeData = async () => {
      setIsLoadingCount(true);
      setIsLoading(true);
      try {
        await Promise.all([
          fetchProductsData(),
          fetchFaqCount(),
          fetchSubscriptionLimits()
        ]);
      } catch (error) {
        console.error('Error initializing data:', error);
      } finally {
        setIsLoadingCount(false);
        setIsLoading(false);
      }
    };

    initializeData();
  }, [currentPage, supabase]); // Add supabase as dependency since we use it in fetchProductsData

  // Function to fetch product count
  const fetchProductCount = async (): Promise<{ totalProducts: number }> => {
    try {
      const response = await fetch('/api/products/count', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch product count');
      }

      const data = await response.json();
      return { totalProducts: data.count };
    } catch (error) {
      console.error('Error fetching product count:', error);
      return { totalProducts: 0 };
    }
  };

  return (
    <div className="min-h-screen bg-black pb-16">
      <div className="flex-grow container mx-auto px-4 pt-20 pb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard/knowledge" className="text-sm text-zinc-400 hover:text-white">
                ← Back
              </Link>
              <h1 className="text-2xl md:text-3xl font-bold font-title">
                Product List
              </h1>
            </div>
          </div>

          {/* Stats Section - Knowledge Stats */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-3 sm:p-6">
              <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Knowledge Stats</h2>

              {/* Modern Circle Stats Grid */}
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                {/* Business Insight Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgb(63, 63, 73)"
                        strokeWidth="6"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(129, 82, 228, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 * (1 - ((totalFaqs ?? 0) / (totalFaqsLimit || 1)))}
                        transform="rotate(-90 50 50)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <FaBrain className="w-5 h-5 sm:w-8 sm:h-8 text-jade-purple" />
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">Brain</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{totalFaqs ?? 0} <span className="text-zinc-400">/ {totalFaqsLimit || 0}</span></>
                    }
                  </p>
                </div>

                {/* Product List Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgb(63, 63, 73)"
                        strokeWidth="6"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(129, 82, 228, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 * (1 - (productListCount / (productListLimit || 1)))}
                        transform="rotate(-90 50 50)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <FaListAlt className="w-5 h-5 sm:w-8 sm:h-8 text-jade-purple" />
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">Product List</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{productListCount} <span className="text-zinc-400">/ {productListLimit}</span></>
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-3 sm:p-6">
              <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Knowledge Management</h2>

              {/* Buttons Grid */}
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <Link
                  href="/dashboard/knowledge"
                  className="bg-zinc-800 hover:bg-zinc-700 text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Business Insight
                </Link>
                <Link
                  href="/dashboard/knowledge/productList"
                  className="bg-jade-purple hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Product List
                </Link>
              </div>
            </div>
          </div>

          {/* Product Types Management Section */}
          <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 mb-6">
            <div className="flex items-center justify-between mb-0">
              <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Product Types</h2>
              <button
                onClick={() => setShowAddTypeInput(!showAddTypeInput)}
                className="bg-jade-purple hover:bg-jade-purple-dark text-white w-8 h-8 rounded-lg transition-colors font-body flex items-center justify-center text-[10px] sm:text-xs"
                aria-label={showAddTypeInput ? "Hide add type form" : "Show add type form"}
              >
                {showAddTypeInput ? <FaMinus className="w-2 h-2" /> : <FaPlus className="w-2 h-2" />}
              </button>
            </div>

            {/* Add Type Form */}
            {showAddTypeInput && (
              <div className="mb-6 p-4 bg-zinc-800/50 rounded-lg">
                <form onSubmit={handleAddProductType} className="flex gap-2">
                  <input
                    type="text"
                    value={newTypeValue}
                    onChange={(e) => setNewTypeValue(e.target.value)}
                    placeholder="Enter new product type"
                    className="flex-grow bg-zinc-900 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                    autoFocus
                  />
                  <button
                    type="submit"
                    className="bg-jade-purple hover:bg-jade-purple-dark text-white px-4 py-2 rounded-lg transition-colors font-body text-[10px] sm:text-xs"
                  >
                    Add Type
                  </button>
                </form>
              </div>
            )}

            {/* Types list */}
            {productTypes.length > 0 ? (
              <div className="flex flex-wrap gap-3 mb-2">
                {productTypes.map(type => (
                  <div key={type} className="bg-zinc-800 text-white px-4 py-2 rounded-lg text-sm flex items-center gap-2">
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                    <button
                      onClick={() => handleDeleteProductType(type)}
                      className="text-zinc-400 hover:text-red-400"
                    >
                      <FaTrash className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 bg-zinc-800/30 rounded-lg text-zinc-400 text-sm">
                No product types added yet. Add your first type using the form above.
              </div>
            )}
          </div>

          {/* Product List Content Section */}
          <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 mb-6">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Product Catalog</h2>
              <button
                onClick={toggleAddForm}
                className="bg-jade-purple hover:bg-jade-purple-dark text-white px-3 py-2 rounded-lg transition-colors font-body text-[10px] sm:text-xs"
              >
                {showAddForm ? 'Cancel' : 'Add Product'}
              </button>
            </div>
            {/* <p className="text-zinc-400 text-sm mb-4 md:mb-6 font-body">
              Manage and organize your product catalog and inventory.
            </p>
             */}
            {/* Product Catalog Form */}
            <AnimatePresence>
              {showAddForm && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-zinc-800/50 rounded-lg p-6 mb-8"
                >
                  <h3 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center sm:text-left">Add New Product</h3>

                  <form className="space-y-6" onSubmit={handleSubmitConfirm}>
                    {/* Photo Upload and Form Fields - Responsive Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 md:auto-rows-fr gap-4 md:gap-6">
                      {/* Left Column - Photo Upload */}
                      <div className="flex flex-col h-full">
                        <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                          Product Photo
                        </label>
                        <div
                          ref={dropAreaRef}
                          className="border-2 border-dashed border-zinc-700 rounded-lg p-4 flex flex-col items-center justify-center flex-grow hover:border-jade-purple transition-colors cursor-pointer relative"
                          onClick={() => document.getElementById('product-image')?.click()}
                        >
                          {isProcessingImage ? (
                            <div className="flex flex-col items-center justify-center py-8">
                              <div className="w-10 h-10 border-2 border-jade-purple border-t-transparent rounded-full animate-spin mb-2"></div>
                              <p className="text-zinc-400 text-[10px] sm:text-xs font-body">Processing image...</p>
                            </div>
                          ) : imagePreviews.length > 0 ? (
                            <div className="w-full grid grid-cols-2 gap-4 py-4">
                              {imagePreviews.map((preview) => (
                                <div key={preview.id} className="relative group aspect-square">
                                  <div
                                    className="w-full h-full overflow-hidden rounded-lg border border-zinc-700 cursor-pointer"
                                    onClick={(e) => {
                                      e.stopPropagation(); // Prevent triggering file input click
                                      openImagePreview(imagePreviews.map(p => p.url), imagePreviews.findIndex(p => p.id === preview.id));
                                    }}
                                  >
                                    <img
                                      src={preview.url}
                                      alt={`Preview ${preview.id}`}
                                      className="w-full h-full object-cover"
                                    />
                                  </div>
                                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <button
                                      type="button"
                                      className="bg-zinc-800/80 rounded-full p-2 hover:bg-red-500 shadow-lg transition-colors"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleRemoveImage(imagePreviews.findIndex(p => p.id === preview.id), 'add');
                                      }}
                                    >
                                      <FaTrash className="w-3 h-3 text-white" />
                                    </button>
                                  </div>
                                  {/* Image number indicator */}
                                  <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full">
                                    {imagePreviews.findIndex(p => p.id === preview.id) + 1}
                                  </div>
                                </div>
                              ))}
                              {/* Add more images prompt if less than 4 */}
                              {imagePreviews.length < 4 && (
                                <div className="border-2 border-dashed border-zinc-700 rounded-lg flex flex-col items-center justify-center aspect-square hover:border-jade-purple transition-colors">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-zinc-500 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                  </svg>
                                  <p className="text-zinc-500 text-[10px] text-center">Add more</p>
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="py-8 flex flex-col items-center justify-center h-full">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-zinc-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <p className="text-zinc-500 text-[10px] sm:text-xs text-center">
                                Click to upload or drag and drop<br/>
                                <span className="text-[8px] sm:text-[10px]">Up to 4 photos (Max: 5MB each, JPG/PNG)</span>
                              </p>
                            </div>
                          )}
                          <input
                            id="product-image"
                            type="file"
                            accept="image/jpeg,image/jpg,image/png"
                            className="hidden"
                            onChange={handleImageChange}
                            multiple
                          />
                        </div>
                        {uploadError && (
                          <p className="text-red-500 text-[10px] sm:text-xs mt-1">{uploadError}</p>
                        )}
                      </div>

                      {/* Right Column - Form Fields */}
                      <div className="flex flex-col h-full">
                        {/* Product Name */}
                        <div>
                          <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                            Product Name
                          </label>
                          <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className="w-full bg-zinc-900 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                            placeholder="Enter product name"
                          />
                        </div>

                        {/* Price and Type - Side by side */}
                        <div className="grid grid-cols-2 gap-4 mt-4">
                          <div>
                            <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                              Price
                            </label>
                            <div className="relative">
                              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-zinc-500">$</span>
                              <input
                                type="number"
                                name="price"
                                value={formData.price}
                                onChange={handleInputChange}
                                min="0"
                                step="0.01"
                                className="w-full bg-zinc-900 border border-zinc-700 rounded-lg py-2 pl-8 pr-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                                placeholder="0.00"
                              />
                            </div>
                          </div>

                          <div>
                            <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                              Product Type
                            </label>
                            <select
                              name="type"
                              value={formData.type}
                              onChange={handleInputChange}
                              className="w-full bg-zinc-900 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                            >
                              <option value="" disabled className="text-base">Select type</option>
                              {productTypes.map(type => (
                                <option key={type} value={type} className="text-base">
                                  {type.charAt(0).toUpperCase() + type.slice(1)}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        {/* Product Description - Now in right column and grows to fill space */}
                        <div className="flex flex-col flex-grow mt-4">
                          <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                            Description
                          </label>
                          <textarea
                            name="description"
                            value={formData.description}
                            onChange={handleInputChange}
                            className="w-full bg-zinc-900 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors h-full flex-1"
                            placeholder="Enter product description"
                            rows={8}
                          ></textarea>
                        </div>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end gap-3">
                      <button
                        type="button"
                        className="bg-zinc-700 hover:bg-zinc-600 text-white py-2 px-3 rounded-lg transition-colors font-body text-[10px] sm:text-xs"
                        onClick={handleCancelForm}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        onClick={handleSubmitConfirm}
                        className="bg-jade-purple hover:bg-jade-purple-dark text-white py-2 px-3 rounded-lg transition-colors font-body text-[10px] sm:text-xs"
                      >
                        Save Product
                      </button>
                    </div>
                  </form>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Update Product Form */}
            {showUpdateForm && (
              <div className="bg-zinc-800/50 rounded-lg p-6 mb-8">
                <h3 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center sm:text-left">Update Product</h3>

                <form className="space-y-6" onSubmit={handleUpdateSubmitConfirm}>
                  {/* Photo Upload and Form Fields - Responsive Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 md:auto-rows-fr gap-4 md:gap-6">
                    {/* Left Column - Photo Upload */}
                    <div className="flex flex-col h-full">
                      <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                        Product Photo
                      </label>
                      <div
                        ref={dropAreaRef}
                        className="border-2 border-dashed border-zinc-700 rounded-lg p-4 flex flex-col items-center justify-center flex-grow hover:border-jade-purple transition-colors cursor-pointer relative"
                        onClick={() => document.getElementById('update-product-image')?.click()}
                      >
                        {isProcessingImage ? (
                          <div className="flex flex-col items-center justify-center py-8">
                            <div className="w-10 h-10 border-2 border-jade-purple border-t-transparent rounded-full animate-spin mb-2"></div>
                            <p className="text-zinc-400 text-[10px] sm:text-xs font-body">Processing image...</p>
                          </div>
                        ) : imagePreviews.length > 0 ? (
                          <div className="w-full grid grid-cols-2 gap-4 py-4">
                            {imagePreviews.map((preview) => (
                              <div key={preview.id} className="relative group aspect-square">
                                <div
                                  className="w-full h-full overflow-hidden rounded-lg border border-zinc-700 cursor-pointer"
                                  onClick={(e) => {
                                    e.stopPropagation(); // Prevent triggering file input click
                                    openImagePreview(imagePreviews.map(p => p.url), imagePreviews.findIndex(p => p.id === preview.id));
                                  }}
                                >
                                  <img
                                    src={preview.url}
                                    alt={`Preview ${preview.id}`}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <button
                                    type="button"
                                    className="bg-zinc-800/80 rounded-full p-2 hover:bg-red-500 shadow-lg transition-colors"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleRemoveImage(imagePreviews.findIndex(p => p.id === preview.id), 'update');
                                    }}
                                  >
                                    <FaTrash className="w-3 h-3 text-white" />
                                  </button>
                                </div>
                                {/* Image number indicator */}
                                <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full">
                                  {imagePreviews.findIndex(p => p.id === preview.id) + 1}
                                </div>
                              </div>
                            ))}
                            {/* Add more images prompt if less than 4 */}
                            {imagePreviews.length < 4 && (
                              <div className="border-2 border-dashed border-zinc-700 rounded-lg flex flex-col items-center justify-center aspect-square hover:border-jade-purple transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-zinc-500 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <p className="text-zinc-500 text-[10px] text-center">Add more</p>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="py-8 flex flex-col items-center justify-center h-full">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-zinc-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <p className="text-zinc-500 text-[10px] sm:text-xs text-center">
                              Click to upload or drag and drop<br/>
                              <span className="text-[8px] sm:text-[10px]">Up to 4 photos (Max: 5MB each, JPG/PNG)</span>
                            </p>
                          </div>
                        )}
                        <input
                          id="update-product-image"
                          type="file"
                          accept="image/jpeg,image/jpg,image/png"
                          className="hidden"
                          onChange={handleUpdateImageChange}
                          multiple
                        />
                      </div>
                      {uploadError && (
                        <p className="text-red-500 text-[10px] sm:text-xs mt-1">{uploadError}</p>
                      )}
                    </div>

                    {/* Right Column - Form Fields */}
                    <div className="flex flex-col h-full">
                      {/* Product Name */}
                      <div>
                        <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                          Product Name
                        </label>
                        <input
                          type="text"
                          name="name"
                          value={updateFormData.name}
                          onChange={(e) => setUpdateFormData(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full bg-zinc-900 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                          placeholder="Enter product name"
                        />
                      </div>

                      {/* Price and Type - Side by side */}
                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div>
                          <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                            Price
                          </label>
                          <div className="relative">
                            <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-zinc-500">$</span>
                            <input
                              type="number"
                              name="price"
                              value={updateFormData.price}
                              onChange={(e) => setUpdateFormData(prev => ({ ...prev, price: e.target.value }))}
                              min="0"
                              step="0.01"
                              className="w-full bg-zinc-900 border border-zinc-700 rounded-lg py-2 pl-8 pr-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                              placeholder="0.00"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                            Product Type
                          </label>
                          <select
                            name="type"
                            value={updateFormData.type}
                            onChange={(e) => setUpdateFormData(prev => ({ ...prev, type: e.target.value }))}
                            className="w-full bg-zinc-900 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                          >
                            <option value="" disabled className="text-base">Select type</option>
                            {productTypes.map(type => (
                              <option key={type} value={type} className="text-base">
                                {type.charAt(0).toUpperCase() + type.slice(1)}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Product Description - Now in right column and grows to fill space */}
                      <div className="flex flex-col flex-grow mt-4">
                        <label className="block text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">
                          Description
                        </label>
                        <textarea
                          name="description"
                          value={updateFormData.description}
                          onChange={(e) => setUpdateFormData(prev => ({ ...prev, description: e.target.value }))}
                          className="w-full bg-zinc-900 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors h-full flex-1"
                          placeholder="Enter product description"
                          rows={8}
                        ></textarea>
                      </div>

                      <div className="flex items-center space-x-2 mt-4">
                        <span className="bg-zinc-700 text-zinc-300 px-2 py-1 rounded text-[10px] sm:text-xs">
                          Product ID: {updateFormData.prod_id}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end gap-3">
                    <button
                      type="button"
                      className="bg-zinc-700 hover:bg-zinc-600 text-white py-2 px-3 rounded-lg transition-colors font-body text-[10px] sm:text-xs"
                      onClick={handleCancelUpdateForm}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="bg-jade-purple hover:bg-jade-purple-dark text-white py-2 px-3 rounded-lg transition-colors font-body text-[10px] sm:text-xs"
                    >
                      Update Product
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Product List */}
            <div className="mt-4">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
                {/* <h3 className="text-base sm:text-lg font-medium">Products</h3> */}

                {/* Search & Filter Controls */}
                <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                  {/* Search */}
                  <div className="relative flex-grow">
                    <input
                      type="text"
                      placeholder="Search products..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full bg-zinc-800 border border-zinc-700 rounded-lg py-2 px-3 pl-9 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                    />
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-zinc-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>

                  {/* Type Filter */}
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="bg-zinc-800 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                  >
                    <option value="" className="text-base">All Types</option>
                    {productTypes.map(type => (
                      <option key={type} value={type} className="text-base">
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>

                  {/* Sort Options */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="bg-zinc-800 border border-zinc-700 rounded-lg py-2 px-3 text-white text-base focus:border-jade-purple focus:outline-none transition-colors"
                  >
                    <option value="name" className="text-base">Sort by Price</option>
                    <option value="price-low" className="text-base">Price: Low to High</option>
                    <option value="price-high" className="text-base">Price: High to Low</option>
                  </select>
                </div>
              </div>

              {products.length === 0 ? (
                // Empty State
                <div className="text-center py-10 bg-zinc-800/30 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-zinc-600 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body">No products added yet</p>
                  <p className="text-zinc-500 text-[8px] sm:text-[10px] mt-1">Products you add will appear here</p>
                </div>
              ) : filteredProducts.length === 0 ? (
                // No results after filtering
                <div className="text-center py-10 bg-zinc-800/30 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-zinc-600 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body">No products match your filters</p>
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setFilterType('');
                      setSortBy('name');
                    }}
                    className="text-white hover:bg-jade-purple-dark text-[10px] sm:text-xs mt-2 bg-jade-purple/80 rounded-lg px-3 py-2"
                  >
                    Clear filters
                  </button>
                </div>
              ) : (
                // Product Grid with Pagination
                <>
                  <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                    {paginatedProducts.map(product => (
                      <div key={product.id} className="bg-zinc-800/50 rounded-lg overflow-hidden border border-zinc-700 hover:border-jade-purple transition-all duration-300 group">
                        {/* Product Image */}
                        <div className="h-48 overflow-hidden bg-zinc-900 relative">
                          {/* Main product image - ONLY use photo_urls */}
                          <img
                            src={product.photo_urls && product.photo_urls.length > 0 ?
                                (() => {
                                  const url = product.photo_urls[0];
                                  console.log('DISPLAYING IMAGE URL:', {
                                    prod_id: product.prod_id,
                                    url: url,
                                    isSupabaseStorage: url?.includes('supabase.co/storage/v1') || false,
                                    fullUrl: url // Log the full URL
                                  });
                                  return url;
                                })() :
                                '/placeholder-product.jpg'}
                            alt={product.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500 cursor-pointer"
                            onClick={() => {
                              // Show the full-size image when clicked - only use photo_urls
                              const images = product.photo_urls && product.photo_urls.length > 0
                                ? product.photo_urls
                                : ['/images/placeholder-product.jpg'];
                              console.log('Opening image preview for:', product.prod_id, images);
                              openImagePreview(images, 0);
                            }}
                            onError={(e) => {
                              console.error('Image failed to load:', {
                                prod_id: product.prod_id,
                                src: (e.target as HTMLImageElement).src
                              });
                              // Optionally set a fallback image
                              (e.target as HTMLImageElement).src = '/placeholder-product.jpg';
                            }}
                          />

                          {/* Multiple image indicator - only show if has multiple images */}
                          {(product.photo_urls && product.photo_urls.length > 1) && (
                            <div className="absolute bottom-2 left-2 bg-black/60 text-white px-2 py-1 rounded-full text-xs">
                              <span>
                                {product.photo_urls.length} photos
                              </span>
                            </div>
                          )}

                          <div className="absolute top-2 right-2 space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button
                              className="bg-zinc-800/80 p-2 rounded-full hover:bg-jade-purple transition-colors"
                              onClick={() => handleEditProduct(product)}
                            >
                              <FaEdit className="w-4 h-4 text-white" />
                            </button>
                            <button
                              className="bg-zinc-800/80 p-2 rounded-full hover:bg-red-500 transition-colors"
                              onClick={() => handleDeleteProduct(product.prod_id, product.photo_file_paths)}
                            >
                              <FaTrash className="w-4 h-4 text-white" />
                            </button>
                          </div>
                        </div>

                        {/* Product Content */}
                        <div className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="font-medium text-white line-clamp-1 text-[10px] sm:text-sm">{product.name}</h4>
                            <span className="text-[8px] sm:text-xs bg-zinc-700 px-2 py-1 rounded text-zinc-300">
                              {product.prod_id}
                            </span>
                          </div>
                          <p className="text-zinc-400 text-[8px] sm:text-xs mb-3 line-clamp-2">{product.description}</p>
                          <div className="flex justify-between items-center">
                            <span className="text-white font-medium text-[10px] sm:text-sm">${product.price.toFixed(2)}</span>
                            <span className="bg-jade-purple text-white px-2 py-1 rounded text-[8px] sm:text-xs">
                              {product.type}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Pagination Controls */}
                  {totalPages > 1 && (
                    <div className="flex justify-center mt-6 gap-2 items-center">
                      <button
                        onClick={handlePreviousPage}
                        disabled={currentPage === 1}
                        className={`px-3 py-1 rounded text-[10px] sm:text-xs ${currentPage === 1 ? 'bg-zinc-800/50 text-zinc-600 cursor-not-allowed' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`}
                      >
                        ← Prev
                      </button>

                      <div className="hidden sm:flex gap-1 items-center">
                        {renderPaginationButtons()}
                      </div>

                      <span className="sm:hidden text-[10px] sm:text-xs text-zinc-400">
                        {currentPage} / {totalPages}
                      </span>

                      <button
                        onClick={handleNextPage}
                        disabled={currentPage === totalPages}
                        className={`px-3 py-1 rounded text-[10px] sm:text-xs ${currentPage === totalPages ? 'bg-zinc-800/50 text-zinc-600 cursor-not-allowed' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`}
                      >
                        Next →
                      </button>
                    </div>
                  )}
                </>
              )}

              {/* Product count summary */}
              {products.length > 0 && (
                <div className="mt-4 text-[8px] sm:text-xs text-zinc-500">
                  Showing {Math.min(filteredProducts.length, (currentPage - 1) * itemsPerPage + 1)} - {Math.min(filteredProducts.length, currentPage * itemsPerPage)} of {filteredProducts.length} products
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
      <Footer />

      {/* Status Overlay */}
      <AnimatePresence>
        {updateStatus !== 'idle' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            ref={statusOverlayRef}
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            onClick={(e) => {
              if (statusOverlayRef.current === e.target) {
                if (updateStatus === 'success' || updateStatus === 'error') {
                  setUpdateStatus('idle');
                }
              }
            }}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className={`p-6 rounded-xl max-w-md w-full mx-4 ${
                updateStatus === 'loading' ? 'bg-jade-purple/30 border border-jade-purple/50 text-white' :
                updateStatus === 'success' ? 'bg-green-500/20 border border-green-500/30 text-green-400' :
                'bg-red-500/20 border border-red-500/30 text-red-400'
              } backdrop-blur-md`}
              onClick={(e) => e.stopPropagation()}
            >
              {updateStatus === 'loading' ? (
                <div className="flex flex-col items-center text-center">
                  <div className="w-10 h-10 mb-4 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-lg font-semibold mb-3">Processing...</p>
                  <div className="w-full bg-black/30 rounded-full h-3 mb-1">
                    <div
                      className="bg-white h-3 rounded-full transition-all duration-300"
                      style={{ width: `${updateProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-sm">{updateProgress}% complete</p>
                  <p className="text-sm mt-2">{updateMessage}</p>
                </div>
              ) : (
                <div className="text-center">
                  <div className={updateStatus === 'success' ? 'text-green-400' : 'text-red-400'}>
                    {updateStatus === 'success' ? (
                      <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <p className="text-lg font-semibold mb-1">{updateStatus === 'success' ? 'Success!' : 'Error'}</p>
                  <p>{updateMessage}</p>
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Confirmation Modal */}
      <AnimatePresence>
        {showConfirmation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
          >
            <motion.div
              ref={confirmModalRef}
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 w-full max-w-md mx-4"
            >
              <h3 className="text-xl font-bold mb-4 font-title text-center">
                {showUpdateForm ? 'Update Product' : 'Add New Product'}
              </h3>
              <p className="text-zinc-300 mb-6 text-center">
                Are you sure you want to {showUpdateForm ? 'update' : 'add'} this product to your catalog?
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowConfirmation(false)}
                  className="flex-1 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-lg transition-colors text-[10px] sm:text-xs"
                >
                  Cancel
                </button>
                <button
                  onClick={showUpdateForm ? handleUpdateSubmit : handleSubmit}
                  className="flex-1 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors text-[10px] sm:text-xs"
                >
                  Confirm
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Cancel Confirmation Modal */}
      {showCancelConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
          <div ref={cancelConfirmRef} className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex justify-center mb-4">
              <div className="bg-jade-purple/80 text-zinc-100 rounded-full p-3">
                <FaTrash className="w-4 h-4" />
              </div>
            </div>
            <h3 className="text-xl font-bold mb-4 font-title text-center">
              Cancel {cancelAction === 'add' ? 'Adding' : 'Updating'} Product?
            </h3>
            <p className="text-zinc-300 mb-6 text-center">
              You have an uploaded image. If you cancel now, your changes will be lost.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowCancelConfirm(false)}
                className="flex-1 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-lg transition-colors text-[10px] sm:text-xs"
              >
                Go Back
              </button>
              <button
                onClick={confirmCancel}
                className="flex-1 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors text-[10px] sm:text-xs"
              >
                Yes, Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
          <div ref={deleteModalRef} className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex justify-center mb-4">
              <div className="bg-red-600/20 text-red-500 rounded-full p-3">
                <FaTrash className="w-8 h-8" />
              </div>
            </div>
            <h3 className="text-xl font-bold mb-4 font-title text-center">
              {isDeleting ? 'Deleting Product...' : 'Delete Product'}
            </h3>
            <p className="text-zinc-300 mb-6 text-center">
              {isDeleting
                ? 'Please wait while we delete this product and its associated images. This may take a moment.'
                : 'Are you sure you want to delete this product? This action cannot be undone.'}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setDeleteConfirm(null)}
                className={`flex-1 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-lg transition-colors text-[10px] sm:text-xs ${
                  isDeleting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={isDeleting}
                style={{ display: isDeleting ? 'none' : 'block' }}
              >
                Cancel
              </button>
              {isDeleting ? (
                <button
                  className="flex-1 py-2 bg-red-600 text-white rounded-lg transition-colors flex justify-center items-center text-[10px] sm:text-xs cursor-wait"
                  disabled={true}
                >
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Deleting...
                </button>
              ) : (
                <button
                  onClick={handleDeleteFromDatabase}
                  className="flex-1 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex justify-center items-center text-[10px] sm:text-xs"
                >
                  Delete
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Image Gallery Modal */}
      {previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50">
          <div ref={imageGalleryRef} className="relative bg-zinc-900/80 border border-zinc-800 rounded-xl p-6 w-full max-w-3xl mx-4">
            {/* Close button in the top-right corner */}
            <button
              onClick={() => setPreviewImage(null)}
              className="absolute top-3 right-3 p-1.5 rounded-full bg-zinc-800 hover:bg-zinc-700 text-zinc-400 hover:text-white transition-colors z-20"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="flex flex-col">
              {/* Image counter */}
              <div className="text-center mb-2 text-sm text-zinc-400">
                {currentImageIndex + 1} / {previewImages.length}
              </div>

              {/* Main image container with touch events */}
              <div
                className="relative h-[60vh] flex items-center justify-center"
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                aria-live="polite"
                role="region"
                aria-label={`Image ${currentImageIndex + 1} of ${previewImages.length}`}
              >
                {previewImages.length === 0 ? (
                  <div className="text-center text-zinc-400 p-8 bg-zinc-800/50 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-zinc-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p>No images available</p>
                  </div>
                ) : (
                  <div className={`relative ${isZoomed ? 'cursor-zoom-out' : 'cursor-zoom-in'}`}>
                    <img
                      src={previewImage}
                      alt={`Image ${currentImageIndex + 1} of ${previewImages.length}`}
                      className={`
                        rounded-lg transition-all duration-300 ease-in-out
                        ${isZoomed ? 'max-w-none max-h-none scale-150' : 'max-w-full max-h-full object-contain'}
                      `}
                      onClick={handleImageClick}
                      onError={(e) => {
                        console.log('Gallery image load error:', e);
                        (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                      }}
                    />
                    {isZoomed && (
                      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black/70 text-white text-xs py-1 px-3 rounded-full">
                        Click to zoom out
                      </div>
                    )}
                  </div>
                )}

                {/* Navigation buttons - only show if more than one image */}
                {previewImages.length > 1 && (
                  <>
                    {/* Previous button */}
                    <button
                      onClick={() => {
                        showPreviousImage();
                      }}
                      className="absolute left-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Previous image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {/* Next button */}
                    <button
                      onClick={() => {
                        showNextImage();
                      }}
                      className="absolute right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Next image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}
              </div>

              {/* Thumbnail strip - only show if more than one image */}
              {previewImages.length > 1 && (
                <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                  {previewImages.map((url, index) => (
                    <div
                      key={index}
                      onClick={() => {
                        setCurrentImageIndex(index);
                        setPreviewImage(url);
                        setIsZoomed(false); // Reset zoom state
                      }}
                      className={`w-16 h-16 rounded overflow-hidden cursor-pointer flex-shrink-0 border-2 transition-all ${
                        index === currentImageIndex ? 'border-jade-purple scale-105' : 'border-zinc-700 opacity-60 hover:opacity-100'
                      }`}
                    >
                      <img
                        src={url}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Error Modal */}
      {errorModalOpen && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 px-4">
          <div
            ref={errorModalRef}
            className="bg-zinc-900 border border-red-500/30 rounded-lg p-6 max-w-md w-full flex flex-col items-center"
          >
            <h3 className="text-xl font-bold text-red-400 mb-4 text-center">Error</h3>
            <p className="text-white mb-6 text-center">{errorMessage}</p>
            <div className="flex justify-center">
              <button
                onClick={() => setErrorModalOpen(false)}
                className="px-4 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-lg transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}