'use client'

import { useState } from 'react'
import Link from 'next/link'
import Footer from '@/components/Footer'

export default function IntroPage() {
  console.log('IntroPage component is rendering...');

  return (
    <div className="min-h-screen bg-blue-900 flex flex-col relative pb-16">
      {/* Simplified Header */}
      <header className="relative">
        <div className="container mx-auto px-4 py-4">
          <div className="bg-white/10 rounded-lg px-4 py-3 border border-white/20">
            <div className="flex justify-center items-center">
              <Link href="/dashboard" className="text-white text-xl font-bold">
                Chhlat Bot
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <div>
          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <Link
                href="/dashboard"
                className="inline-flex items-center gap-2 px-3 py-2 text-sm text-white bg-blue-800 hover:bg-blue-700 border border-blue-600 rounded-lg"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold text-white">
                Intro Message
              </h1>

              <div className="w-20"></div>
            </div>
          </div>

          {/* Success Message */}
          <div className="bg-green-600/20 border border-green-500/30 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-bold text-green-400 mb-4">✅ Page Fixed Successfully!</h2>
            <div className="text-white space-y-2">
              <p>• All outro-related code has been removed</p>
              <p>• Syntax errors have been fixed</p>
              <p>• Component is now rendering properly</p>
              <p>• Next.js cache has been cleared</p>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-white/10 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-bold text-white mb-4">Next Steps</h2>
            <p className="text-white/70 mb-4">
              The intro page is now working. You can now restore the full functionality 
              by adding back the intro-specific features you need.
            </p>
            <Link 
              href="/dashboard" 
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
            >
              Back to Dashboard
            </Link>
          </div>

        </div>
      </div>

      <Footer />
    </div>
  );
}
