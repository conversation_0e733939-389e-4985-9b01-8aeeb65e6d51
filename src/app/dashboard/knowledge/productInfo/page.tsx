'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { getClientInfo } from '@/utils/client'
import { v4 as uuidv4 } from 'uuid'
import { FaQuestionCircle, FaInfoCircle, FaListAlt, FaBrain } from 'react-icons/fa'

export default function ProductInfoPage() {
  const supabase = createClientComponentClient()
  const [question, setQuestion] = useState('')
  const [answer, setAnswer] = useState('')
  const [recentItems, setRecentItems] = useState<Array<{
    id: number,
    product_name: string,
    product_info: string,
    audioUrl?: string,
    audioDuration?: number
  }>>([])
  const [isUpdating, setIsUpdating] = useState(false)
  const [editingItem, setEditingItem] = useState<{id: number, field: 'product_name' | 'product_info', value: string} | null>(null)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)

  const [businessInsightCount, setBusinessInsightCount] = useState(76)
  const [productInfoCount, setProductInfoCount] = useState(42)
  const [productListCount, setProductListCount] = useState(18)
  const [totalFaqs, setTotalFaqs] = useState(10)
  const [isLoadingCount, setIsLoadingCount] = useState(true)

  const modalRef = useRef<HTMLDivElement>(null)
  const confirmModalRef = useRef<HTMLDivElement>(null)
  const statusOverlayRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const [viewingItem, setViewingItem] = useState<{field: 'product_name' | 'product_info', value: string} | null>(null)
  const viewModalRef = useRef<HTMLDivElement>(null)

  const [isRecording, setIsRecording] = useState(false)
  const [recordingFor, setRecordingFor] = useState<'product_info' | null>(null)
  const [recordingTime, setRecordingTime] = useState(0)
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null)
  const [audioRecorder, setAudioRecorder] = useState<MediaRecorder | null>(null)
  const [audioChunks, setAudioChunks] = useState<Blob[]>([])
  const [itemAudioUrl, setItemAudioUrl] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState<'product_info' | null>(null)
  const [playbackTime, setPlaybackTime] = useState(0)
  const [audioDuration, setAudioDuration] = useState(0)
  const [playbackProgress, setPlaybackProgress] = useState(0)
  const [isSaving, setIsSaving] = useState(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const playbackTimerRef = useRef<number | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  const [recordingError, setRecordingError] = useState<string | null>(null)

  const [playingItemId, setPlayingItemId] = useState<number | null>(null)

  const [audioContext, setAudioContext] = useState<AudioContext | null>(null)
  const [audioInitialized, setAudioInitialized] = useState(false)

  const stopPlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    if (playbackTimerRef.current) {
      cancelAnimationFrame(playbackTimerRef.current);
      playbackTimerRef.current = null;
    }
    setIsPlaying(null);
    setPlayingItemId(null);
    setPlaybackTime(0);
    setPlaybackProgress(0);
  };

  const fetchProductInfoCount = async () => {
    setIsLoadingCount(true)
    try {
      const clientId = getClientInfo()?.client_id;
      if (!clientId) {
        console.error('Client ID not found while fetching product info count');
        setProductInfoCount(0);
        return;
      }

      const { count, error } = await supabase
        .from('product_info')
        .select('*', { count: 'exact', head: true })
        .eq('client_id', clientId);

      if (error) {
        console.error('Error fetching product info count:', error);
        setProductInfoCount(0);
        return;
      }

      setProductInfoCount(count || 0);
    } catch (error) {
      console.error('Error in fetchProductInfoCount:', error);
      setProductInfoCount(0);
    } finally {
      setIsLoadingCount(false);
    }
  }

  useEffect(() => {
    fetchProductInfoCount();
  }, []);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        handleSaveEdit()
      }
    }

    if (editingItem) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [editingItem])

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (viewModalRef.current && !viewModalRef.current.contains(event.target as Node)) {
        setViewingItem(null)
      }
    }

    if (viewingItem) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [viewingItem])

  useEffect(() => {
    if (editingItem && textareaRef.current) {
      textareaRef.current.focus()
      const length = textareaRef.current.value.length
      textareaRef.current.setSelectionRange(length, length)
    }
  }, [editingItem])

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (confirmModalRef.current && !confirmModalRef.current.contains(event.target as Node)) {
        setShowConfirmation(false)
      }
    }

    if (showConfirmation) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showConfirmation])

  useEffect(() => {
    return () => {
      if (isRecording && audioRecorder) {
        audioRecorder.stop();
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (audioStream) {
        audioStream.getAudioTracks().forEach(track => track.stop());
      }
      stopPlayback();
    };
  }, [isRecording, audioRecorder, audioStream]);

  const handleStartEdit = (id: number, field: 'product_name' | 'product_info', value: string) => {
    setEditingItem({ id, field, value })
  }

  const handleSaveEdit = () => {
    if (editingItem) {
      if (editingItem.id === -1) {
        setQuestion(editingItem.value);
      } else if (editingItem.id === -2) {
        setAnswer(editingItem.value);
      } else {
        setRecentItems(prev => prev.map(item =>
          item.id === editingItem.id
            ? { ...item, [editingItem.field]: editingItem.value }
            : item
        ));
      }
    }
    setEditingItem(null);
  }

  const handleViewItem = (field: 'product_name' | 'product_info', value: string) => {
    setViewingItem({ field, value })
  }

  const handleAddItem = () => {
    if (question.trim() && (answer.trim() || itemAudioUrl)) {
      setRecentItems(prev => [...prev, {
        id: Date.now(),
        product_name: question.trim(),
        product_info: itemAudioUrl ? `[AUDIO:${audioDuration}s]` : answer.trim(),
        audioUrl: itemAudioUrl || undefined,
        audioDuration: itemAudioUrl ? audioDuration : undefined
      }]);

      setQuestion('');
      setAnswer('');
      if (itemAudioUrl) {
        setItemAudioUrl(null);
      }
      setRecordingError(null);
    }
  }

  const handleUpdate = () => {
    if (recentItems.length === 0) {
      setUpdateMessage("No product items to update. Add some first.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    setShowConfirmation(true)
  }

  const saveItemsToSupabase = async () => {
    setShowConfirmation(false);
    setIsUpdating(true);
    setUpdateStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage('Processing additions...');

    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session?.user?.id) {
        throw new Error('Could not verify user session.');
      }
      const userId = session.user.id;

      const clientId = getClientInfo()?.client_id;
      if (!clientId) {
        throw new Error('Client identifier not found.');
      }

      // IMPORTANT: Verify that the client_id belongs to the current authenticated user
      // This is required for the RLS policy to allow the operation
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('client_id')
        .eq('client_id', clientId)
        .eq('auth_id', userId)
        .single();

      if (clientError || !clientData) {
        console.error('Client verification error:', clientError);
        throw new Error('You do not have permission to update product information for this client. Please contact support.');
      }

      setUpdateMessage('Uploading audio files...');
      const audioUrlMap = new Map<number, { publicUrl: string, duration: number, filePath?: string }>();
      const itemsWithAudio = recentItems.filter(item => item.audioUrl && item.audioUrl.startsWith('blob:'));

      for (let i = 0; i < itemsWithAudio.length; i++) {
        const item = itemsWithAudio[i];
        setUpdateProgress(Math.round(((i + 1) / itemsWithAudio.length) * 50));

        if (item.audioUrl) {
          try {
            const response = await fetch(item.audioUrl);
            const audioBlob = await response.blob();
            if (audioBlob.size === 0) continue;

            let fileExtension = 'bin';
            const mimeParts = audioBlob.type.split('/');
            if (mimeParts.length > 1) {
                const subType = mimeParts[1].split(';')[0];
                if (subType === 'opus') fileExtension = 'opus';
                else if (subType === 'webm') fileExtension = 'webm';
                else if (subType === 'mp4') fileExtension = 'mp4';
                else if (subType === 'ogg') fileExtension = 'ogg';
            }

            const uniqueFileName = `${uuidv4()}.${fileExtension}`;
            const filePath = `${userId}/${uniqueFileName}`;

            const { data: uploadData, error: uploadError } = await supabase
              .storage
              .from('audios')
              .upload(filePath, audioBlob, { cacheControl: '3600', upsert: false });

            if (uploadError) throw new Error(`Failed to upload audio for "${item.product_name.substring(0, 20)}...". ${(uploadError as any).message}`);

            const { data: urlData } = supabase.storage.from('audios').getPublicUrl(filePath);
            if (!urlData?.publicUrl) throw new Error(`Failed to get URL for audio "${item.product_name.substring(0, 20)}...".`);

            audioUrlMap.set(item.id, { publicUrl: urlData.publicUrl, duration: item.audioDuration || 0, filePath: filePath });

          } catch (uploadError) {
            console.error(`Error processing audio for Item ID ${item.id}:`, uploadError);
            throw uploadError;
          }
        }
      }

      setUpdateMessage('Saving product information...');
      const batchSize = 30;
      const totalItems = recentItems.length;
      const batches = Math.ceil(totalItems / batchSize);
      let itemsProcessed = 0;

      for (let i = 0; i < batches; i++) {
        const startIdx = i * batchSize;
        const endIdx = Math.min((i + 1) * batchSize, totalItems);
        const batch = recentItems.slice(startIdx, endIdx);

        const itemsToInsert = batch.map(item => {
          const audioInfo = audioUrlMap.get(item.id);
          const textInfo = audioInfo ? "" : item.product_info;
          const audioUrl = audioInfo ? audioInfo.publicUrl : "";
          const audioFilePath = audioInfo ? audioInfo.filePath : null;

          return {
            product_name: item.product_name,
            product_info: textInfo,
            created_at: new Date(),
            client_id: clientId,
            audio_url: audioUrl,
            audio_file_path: audioFilePath,
            audio_duration: audioInfo ? Math.round(audioInfo.duration) : null,
          };
        });

        const { error: insertError } = await supabase
          .from('product_info')
          .insert(itemsToInsert);

        if (insertError) {
           if (insertError.message.includes('violates not-null constraint') && insertError.message.includes('"product_info"')) {
               throw new Error(`Database requires product info text. Cannot save audio-only entry for "${batch[0]?.product_name.substring(0, 20)}...".`);
           }
           throw new Error(`Failed to save batch starting with "${batch[0]?.product_name.substring(0, 20)}...". ${insertError.message}`);
        }

        itemsProcessed += batch.length;
        setUpdateProgress(50 + Math.round((itemsProcessed / totalItems) * 50));
      }

      await fetchProductInfoCount();

      recentItems.forEach(item => {
        if (item.audioUrl && item.audioUrl.startsWith('blob:')) {
          URL.revokeObjectURL(item.audioUrl);
        }
      });

      setUpdateStatus('success');
      setUpdateMessage(`Successfully added ${totalItems} product item${totalItems > 1 ? 's' : ''}.`);
      setRecentItems([]);

      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 1500);

    } catch (error: any) {
      console.error('Error updating product info:', error);
      setUpdateStatus('error');
      setUpdateMessage(error.message || 'Failed to update product info. Please try again.');
      setIsUpdating(false);
    }
  }

  const handleDelete = (id: number) => {
    const itemToDelete = recentItems.find(item => item.id === id);
    if (itemToDelete?.audioUrl) {
      URL.revokeObjectURL(itemToDelete.audioUrl);
    }
    setRecentItems(prev => prev.filter(item => item.id !== id));
  }

  const startRecording = async (type: 'product_info') => {
    try {
      if (itemAudioUrl) {
        URL.revokeObjectURL(itemAudioUrl);
        setItemAudioUrl(null);
      }
      setAnswer('');
      if (isRecording) stopRecording();
      setRecordingTime(0);

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);
      setRecordingFor(type);

      const supportedTypes = ['audio/mp4', 'audio/webm;codecs=opus', 'audio/webm'];
      let selectedMimeType = '';
      for (const t of supportedTypes) {
        if (MediaRecorder.isTypeSupported(t)) { selectedMimeType = t; break; }
      }
      if (!selectedMimeType) {
         alert("Browser doesn't support compatible audio recording format.");
         stream.getTracks().forEach(track => track.stop());
         setAudioStream(null);
         return;
      }

      const chunks: Blob[] = [];
      setAudioChunks([]);
      const recorder = new MediaRecorder(stream, { mimeType: selectedMimeType });
      setAudioRecorder(recorder);

      recorder.ondataavailable = (e) => { if (e.data.size > 0) chunks.push(e.data); };

      recorder.onstop = () => {
        const audioBlob = new Blob(chunks, { type: selectedMimeType });
        const audioUrl = URL.createObjectURL(audioBlob);
        setItemAudioUrl(audioUrl);
        if (audioStream) {
          audioStream.getTracks().forEach(track => track.stop());
          setAudioStream(null);
        }
      };

      setIsRecording(true);
      if (timerRef.current) clearInterval(timerRef.current);

      recorder.start();
      setTimeout(() => {
        timerRef.current = setInterval(() => setRecordingTime(prev => prev + 1), 1000);
      }, 100);

    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Unable to access microphone. Check permissions.');
    }
  };

  const stopRecording = () => {
    if (audioRecorder && audioRecorder.state !== 'inactive') {
      try {
        setAudioDuration(recordingTime);
        if (timerRef.current) clearInterval(timerRef.current);
        audioRecorder.stop();
        setIsRecording(false);
        setRecordingFor(null);

        setIsSaving(true);
        setTimeout(() => {
          setIsSaving(false);
          if (recordingTime > 60) {
            setRecordingError("Over 60s");
             if (itemAudioUrl) {
               URL.revokeObjectURL(itemAudioUrl);
               setItemAudioUrl(null);
             }
          } else {
            setRecordingError(null);
            setAnswer(`[AUDIO:${audioDuration}s]`);
          }
        }, 1500);
      } catch (error) {
        console.error('Error stopping recording:', error);
      }
    } else {
      setIsRecording(false);
      setRecordingFor(null);
      if (timerRef.current) clearInterval(timerRef.current);
    }
  };

  const initializeAudio = () => {
    if (audioInitialized || typeof window === 'undefined') return;
    try {
      const context = new (window.AudioContext || (window as any).webkitAudioContext)();
      setAudioContext(context);
      setAudioInitialized(true);
      if (context.state === 'suspended') {
        context.resume().catch(e => console.error('Error resuming AudioContext:', e));
      }
    } catch (e) {
      console.error('Error initializing AudioContext:', e);
      setRecordingError('Failed to initialize audio.');
    }
  };

  useEffect(() => {
    const initAudioOnInteraction = () => {
      if (!audioInitialized) {
        initializeAudio();
        window.removeEventListener('touchstart', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
        window.removeEventListener('mousedown', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
      }
    };
    if (typeof window !== 'undefined' && !audioInitialized) {
       window.addEventListener('touchstart', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
       window.addEventListener('mousedown', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
    }
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('touchstart', initAudioOnInteraction);
        window.removeEventListener('mousedown', initAudioOnInteraction);
      }
    };
  }, [audioInitialized, initializeAudio]);

  useEffect(() => {
    const handleVisibilityChange = () => { if (document.hidden) stopPlayback(); };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [stopPlayback]);

  const playRecording = (type: 'product_info') => {
    initializeAudio();
    if (audioContext?.state === 'suspended') audioContext.resume();

    const audioUrl = itemAudioUrl;
    if (!audioUrl) return;

    if (isPlaying === type) stopPlayback(); return;
    stopPlayback();

    const audio = new Audio();
    audioRef.current = audio;

    setIsPlaying(type);
    setPlaybackTime(audioDuration);
    setPlaybackProgress(0);

    audio.addEventListener('timeupdate', () => {
      if (!audioRef.current) return;
      const currentTime = audioRef.current.currentTime;
      setPlaybackTime(Math.ceil(audioDuration - currentTime));
      setPlaybackProgress(Math.min(100, (currentTime / audioDuration) * 100));
    });
    audio.addEventListener('ended', stopPlayback);
    audio.addEventListener('error', (e) => { console.error('Audio playback error:', e); stopPlayback(); });

    audio.src = audioUrl!;
    audio.load();
    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          console.log('[playRecording] Playback started successfully via promise.');
        })
        .catch(err => {
          console.error('[playRecording] Play promise rejected:', err);
          stopPlayback();
        });
    } else {
       console.warn('[playRecording] audio.play() did not return a promise.');
    }
  };

  const formatRecordingTime = (seconds: number) => {
    return `${seconds}s`;
  };

  const playRecentItemAudio = (itemId: number, audioUrl: string, duration: number) => {
     initializeAudio();
     if (audioContext?.state === 'suspended') audioContext.resume();

    if (playingItemId === itemId) { stopPlayback(); return; }
    stopPlayback();

    const audio = new Audio();
    audioRef.current = audio;

    setPlayingItemId(itemId);
    setPlaybackTime(duration);
    setPlaybackProgress(0);

    const updateHandler = () => {
      if (!audioRef.current) return;
      const currentTime = audioRef.current.currentTime;
      const remaining = Math.max(0, Math.ceil(duration - currentTime));
      setPlaybackTime(remaining);
      setPlaybackProgress(Math.min(100, (currentTime / duration) * 100));
    };

    const endedHandler = () => {
      console.log('[playRecentAudio] Audio ended for QA ID:', itemId);
      if (audioRef.current) {
        audioRef.current.removeEventListener('timeupdate', updateHandler);
        audioRef.current.removeEventListener('ended', endedHandler);
        audioRef.current.removeEventListener('error', errorHandler);
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setPlayingItemId(null);
      setPlaybackProgress(0);
      setPlaybackTime(0);
    };

    const errorHandler = (e: Event) => {
      console.error('[playRecentAudio] Audio playback error:', e);
      if (audioRef.current) {
        audioRef.current.removeEventListener('timeupdate', updateHandler);
        audioRef.current.removeEventListener('ended', endedHandler);
        audioRef.current.removeEventListener('error', errorHandler);
      }
      stopPlayback();
    };

    audio.addEventListener('timeupdate', updateHandler);
    audio.addEventListener('ended', endedHandler);
    audio.addEventListener('error', errorHandler);

    audio.src = audioUrl;
    audio.load();
    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise.catch(err => {
        console.error('[playRecentAudio] Play promise error:', err);
        stopPlayback();
      });
    }
  };

  return (
    <div className="min-h-screen bg-black flex flex-col">
      <div className="flex-grow container mx-auto px-4 pt-20 pb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="text-sm text-zinc-400 hover:text-white">
                ← Back
              </Link>
              <h1 className="text-2xl md:text-3xl font-bold font-title">
                <span className="gradient-text">Product</span> Info
              </h1>
            </div>
          </div>

          {/* Top Section - Updated for mobile responsiveness */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-3 sm:p-6">
              <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Knowledge Stats</h2>

              {/* Modern Circle Stats Grid */}
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                {/* Business Insight Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgb(63, 63, 73)"
                        strokeWidth="6"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(129, 82, 228, 0.9)"
                        strokeWidth="6"
                        strokeDasharray={`${Math.min((businessInsightCount / 1000) * 251.2, 251.2)} 251.2`}
                        strokeDashoffset="0"
                        transform="rotate(-90 50 50)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <FaBrain className="w-5 h-5 sm:w-8 sm:h-8 text-jade-purple" />
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">Brain</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{businessInsightCount} <span className="text-zinc-400">/ 1000</span></>
                    }
                  </p>
                </div>


                {/* Product Info Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgb(63, 63, 73)"
                        strokeWidth="6"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(129, 82, 228, 0.9)"
                        strokeWidth="6"
                        strokeDasharray={`${Math.min((productInfoCount / 1000) * 251.2, 251.2)} 251.2`}
                        strokeDashoffset="0"
                        transform="rotate(-90 50 50)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <FaInfoCircle className="w-5 h-5 sm:w-8 sm:h-8 text-jade-purple" />
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">Product Info</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{productInfoCount} <span className="text-zinc-400">/ 1000</span></>
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-3 sm:p-6">
              <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Knowledge Management</h2>

              {/* Buttons Grid */}
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <Link
                  href="/dashboard/knowledge"
                  className="bg-zinc-800 hover:bg-zinc-700 text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Business Insight
                </Link>
                <Link
                  href="/dashboard/knowledge/productInfo"
                  className="bg-jade-purple hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Product Info
                </Link>
                <Link
                  href="/dashboard/knowledge/productList"
                  className="bg-zinc-800 hover:bg-zinc-700 text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Product List
                </Link>
              </div>
            </div>
          </div>


          <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 mb-6">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-xl font-bold font-title">Add Product Information</h2>
            </div>
            <p className="text-zinc-400 text-sm mb-4 md:mb-6 font-body">
              Add details about your products or services for the AI assistant.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 items-start mb-6">
              <div className="w-full sm:flex-1">
                <div
                  className="px-2 md:px-4 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-500 focus:outline-none focus:border-jade-purple cursor-pointer hover:border-jade-purple flex items-center min-h-[42px]"
                  onClick={() => {
                    setEditingItem({
                      id: -1,
                      field: 'product_name',
                      value: question
                    });
                  }}
                >
                  {question ?
                    <span className="truncate">{question}</span> :
                    <span className="text-zinc-500 truncate">Enter product name or query...</span>
                  }
                </div>
              </div>
              <div className="w-full sm:flex-1">
                <div
                  className={`px-2 md:px-4 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-500 focus:outline-none focus:border-jade-purple cursor-pointer hover:border-jade-purple flex items-center min-h-[42px] relative`}
                  onClick={(e) => {
                    if (isRecording || isPlaying === 'product_info' || itemAudioUrl) {
                      return;
                    }
                    setEditingItem({
                      id: -2,
                      field: 'product_info',
                      value: answer
                    });
                  }}
                >
                  {!isRecording && recordingFor !== 'product_info' && !itemAudioUrl && !isSaving && (
                    answer ?
                      <span className="truncate">{answer}</span> :
                      <span className="text-zinc-500 truncate">Enter product info or record...</span>
                  )}

                  {isRecording && recordingFor === 'product_info' && (
                    <div className="absolute inset-0 bg-jade-purple/50 flex items-center justify-center z-10 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                        <span className="text-white font-mono">{formatRecordingTime(recordingTime)}</span>
                      </div>
                    </div>
                  )}

                  {isSaving && !isRecording && (
                    <div className="absolute inset-0 bg-zinc-800/90 flex items-center justify-center z-20 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-white font-medium">Saving...</span>
                      </div>
                    </div>
                  )}

                  {itemAudioUrl && !isRecording && !isSaving && (
                    <div className="absolute inset-0 bg-zinc-700 flex items-center justify-between px-4 z-10 rounded-lg">
                      {isPlaying === 'product_info' && (
                        <div className="absolute inset-0 bg-jade-purple/90 z-0 rounded-lg" style={{
                          width: `${playbackProgress}%`,
                          transition: 'width 0.1s linear'
                        }}></div>
                      )}

                      <div className="flex items-center space-x-2 flex-grow z-10">
                        {recordingError ? (
                          <span className="text-red-400 truncate">Error: {recordingError}</span>
                        ) : (
                          <>
                            {!recordingError && isPlaying !== 'product_info' && (
                              <button
                                type="button" className="mr-2 p-1 rounded-full bg-jade-purple hover:bg-jade-purple-dark text-white touch-manipulation"
                                style={{ touchAction: "manipulation" }}
                                onClick={() => playRecording('product_info')}
                                onTouchEnd={(e) => { e.preventDefault(); playRecording('product_info'); }}
                                aria-label="Play audio"
                              >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                              </button>
                            )}
                            {isPlaying === 'product_info' && (
                              <button
                                type="button" className="mr-2 p-1 rounded-full bg-zinc-600 hover:bg-zinc-500 text-white"
                                onClick={stopPlayback} onTouchEnd={(e) => { e.preventDefault(); stopPlayback(); }}
                                aria-label="Stop audio"
                              >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="6" y="6" width="12" height="12" rx="2" /></svg>
                              </button>
                            )}
                            <span className="text-white truncate">
                              Audio ({isPlaying === 'product_info' ? playbackTime : audioDuration}s)
                            </span>
                          </>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 z-10">
                        <button
                          type="button" className="p-1 rounded-full bg-red-500 hover:bg-red-600 text-white"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (isPlaying === 'product_info') stopPlayback();
                            if (itemAudioUrl) {
                              URL.revokeObjectURL(itemAudioUrl);
                              setItemAudioUrl(null);
                              setRecordingError(null);
                              setAnswer('');
                            }
                          }}
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>
                        </button>
                      </div>
                    </div>
                  )}

                  <button
                    type="button"
                    className={`absolute right-2 p-1.5 rounded-full transition-colors ${
                      isRecording && recordingFor === 'product_info'
                        ? 'bg-red-500 text-white z-20'
                        : 'bg-zinc-700 text-zinc-400 hover:bg-zinc-600 hover:text-white'
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      if (isRecording && recordingFor === 'product_info') {
                        stopRecording();
                      } else if (!isRecording) {
                        startRecording('product_info');
                      }
                      return false;
                    }}
                    title={isRecording && recordingFor === 'product_info' ? 'Stop recording' : 'Record product info'}
                  >
                    {isRecording && recordingFor === 'product_info' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="6" y="6" width="12" height="12" rx="2" /></svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" /></svg>
                    )}
                  </button>
                </div>
              </div>
              <div className="w-full sm:w-auto flex items-center justify-center sm:justify-end">
                <button
                  onClick={handleAddItem}
                  className="bg-white text-jade-purple hover:bg-zinc-100 hover:shadow-md transition-all duration-200 px-6 py-2 rounded-lg font-medium w-full sm:w-auto"
                  disabled={!question.trim() || (!answer.trim() && !itemAudioUrl) || isRecording || isSaving}
                >
                  Add Item
                </button>
              </div>
            </div>

            <div className="flex justify-between items-center mb-3 border-t border-zinc-800 pt-6">
              <h3 className="text-lg font-semibold font-title">Recently Added Items</h3>
              <button
                onClick={handleUpdate}
                className="bg-white text-jade-purple hover:bg-zinc-100 hover:shadow-md transition-all duration-200 px-6 py-2 rounded-lg font-medium"
                disabled={isUpdating || recentItems.length === 0}
              >
                {isUpdating ? 'Updating...' : 'Update Items'}
              </button>
            </div>

            <div className="flex border-b border-zinc-800 py-3 font-semibold text-zinc-400 font-body">
              <div className="w-[6%] px-2 text-left"></div>
              <div className="w-[43%] px-2 text-left">Product/Query</div>
              <div className="w-[43%] px-2 text-left">Information</div>
              <div className="w-[8%] px-2 text-center"></div>
            </div>

            <div className="w-full">
              {recentItems.length > 0 ? (
                recentItems.map((item, index) => (
                  <div key={item.id} className="flex border-b border-zinc-800 py-3 items-center">
                    <div className="w-[6%] px-2 text-left text-zinc-500 font-body">{index + 1}</div>
                    <div className="w-[42%] px-2 py-1">
                      <div
                        className="h-full w-full truncate break-words px-3 py-1.5 bg-zinc-800 border border-zinc-700 rounded-lg hover:bg-zinc-800/80 hover:border-jade-purple flex items-center group cursor-pointer transition-all"
                        title={item.product_name}
                        onClick={() => handleStartEdit(item.id, 'product_name', item.product_name)}
                      >
                        <span className="flex-1">{item.product_name}</span>
                        <svg className="w-4 h-4 ml-2 text-zinc-500 group-hover:text-jade-purple invisible group-hover:visible" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg>
                      </div>
                    </div>
                    <div className="w-[42%] px-2 py-1">
                      {item.audioUrl ? (
                        <div className="h-full w-full px-3 py-1.5 bg-zinc-800 border border-zinc-700 rounded-lg hover:bg-zinc-800/80 hover:border-jade-purple flex items-center justify-between relative overflow-hidden cursor-pointer transition-all">
                          {playingItemId === item.id && (
                            <div className="absolute inset-0 bg-jade-purple/30 z-0 rounded-lg" style={{ width: `${playbackProgress}%`, transition: 'width 0.1s linear' }}></div>
                          )}
                          <div className="flex items-center z-10 relative">
                            <button
                              className={`mr-2 p-1 rounded-full bg-jade-purple text-white z-10 relative touch-manipulation`}
                              style={{ touchAction: "manipulation" }}
                              onClick={(e) => { e.stopPropagation(); e.preventDefault(); playRecentItemAudio(item.id, item.audioUrl!, item.audioDuration || 0); }}
                              onTouchEnd={(e) => { e.stopPropagation(); e.preventDefault(); playRecentItemAudio(item.id, item.audioUrl!, item.audioDuration || 0); }}
                              aria-label={playingItemId === item.id ? "Pause audio" : "Play audio"}
                            >
                              {playingItemId === item.id ? (
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="6" y="6" width="12" height="12" rx="2" /></svg>
                              ) : (
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                              )}
                            </button>
                            <span className="flex-1">
                              {playingItemId === item.id ? `Audio (${playbackTime}s)` : `Audio (${item.audioDuration || 0}s)`}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div
                          className="h-full w-full truncate break-words px-3 py-1.5 bg-zinc-800 border border-zinc-700 rounded-lg hover:bg-zinc-800/80 hover:border-jade-purple flex items-center group cursor-pointer transition-all"
                          title={item.product_info}
                          onClick={() => handleStartEdit(item.id, 'product_info', item.product_info)}
                        >
                          <span className="flex-1">{item.product_info}</span>
                          <svg className="w-4 h-4 ml-2 text-zinc-500 group-hover:text-jade-purple invisible group-hover:visible" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg>
                        </div>
                      )}
                    </div>
                    <div className="w-[10%] px-2 flex flex-col justify-center space-y-2">
                      <button
                        className="text-red-500 hover:text-red-700 text-sm"
                        onClick={() => handleDelete(item.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="py-8 text-center text-zinc-500">
                  No product items added yet
                </div>
              )}
            </div>
          </div>

        </motion.div>
      </div>

      {updateStatus !== 'idle' && (
        <div
          ref={statusOverlayRef}
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
          onClick={(e) => {
              if (statusOverlayRef.current === e.target) {
              if (updateStatus === 'success' || updateStatus === 'error') {
                  setUpdateStatus('idle');
              }
              }
          }}
          >
          <div
              className={`p-6 rounded-xl max-w-md w-full mx-4 ${
              updateStatus === 'loading' ? 'bg-jade-purple/30 border border-jade-purple/50 text-white' :
              updateStatus === 'success' ? 'bg-green-500/20 border border-green-500/30 text-green-400' :
              'bg-red-500/20 border border-red-500/30 text-red-400'
              } backdrop-blur-md`}
              onClick={(e) => e.stopPropagation()}
          >
              {updateStatus === 'loading' ? (
                  <div className="flex flex-col items-center text-center">
                  <div className="w-10 h-10 mb-4 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-lg font-semibold mb-3">Adding product info...</p>
                  <div className="w-full bg-black/30 rounded-full h-3 mb-1">
                    <div className="bg-white h-3 rounded-full transition-all duration-300" style={{ width: `${updateProgress}%` }}></div>
                  </div>
                  <p className="text-sm">{updateProgress}% complete</p>
                  </div>
              ) : (
                  <div className="text-center">
                  <div className={updateStatus === 'success' ? 'text-green-400' : 'text-red-400'}>
                    {updateStatus === 'success' ? (
                      <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" /></svg>
                    ) : (
                      <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                    )}
                  </div>
                  <p className="text-lg font-semibold mb-1">{updateStatus === 'success' ? 'Success!' : 'Error'}</p>
                  <p>{updateMessage}</p>
                  </div>
              )}
          </div>
          </div>
      )}

      {showConfirmation && (
         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div ref={confirmModalRef} className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 w-full max-w-md mx-4">
               <h3 className="text-xl font-bold mb-4 font-title text-center">
                 Update Product Information
               </h3>
               <p className="text-zinc-300 mb-6 text-center">
                 You are about to add {recentItems.length} product item{recentItems.length !== 1 ? 's' : ''}?
               </p>
               <div className="flex space-x-3">
                 <button onClick={() => setShowConfirmation(false)} className="flex-1 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-lg transition-colors">Cancel</button>
                 <button onClick={saveItemsToSupabase} className="flex-1 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors">Confirm</button>
               </div>
            </div>
         </div>
      )}

      {editingItem && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[53]">
             <div ref={modalRef} className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 w-full max-w-lg mx-4">
               <h3 className="text-xl font-bold mb-4 font-title">
                 Edit {editingItem.field === 'product_name' ? 'Product/Query' : 'Information'}
               </h3>
               <textarea
                 autoFocus
                 ref={textareaRef}
                 value={editingItem.value}
                 onChange={(e) => setEditingItem({...editingItem!, value: e.target.value})}
                 className="w-full px-4 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-500 focus:outline-none focus:border-jade-purple min-h-[100px] mb-4"
               />
               <button onClick={handleSaveEdit} className="bg-jade-purple hover:bg-jade-purple-dark text-white px-6 py-2 rounded-lg font-medium w-full sm:w-auto transition-colors">Done</button>
             </div>
           </div>
      )}

      {viewingItem && (
         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
           <div ref={viewModalRef} className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 w-full max-w-lg mx-4">
             <h3 className="text-xl font-bold mb-4 font-title">
               {viewingItem.field === 'product_name' ? 'Product/Query' : 'Information'}
             </h3>
             <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 text-white mb-4 max-h-[60vh] overflow-y-auto">
               {viewingItem.value}
             </div>
             <button onClick={() => setViewingItem(null)} className="bg-white text-jade-purple hover:bg-zinc-100 hover:shadow-md transition-all duration-200 px-6 py-2 rounded-lg font-medium w-full sm:w-auto">Close</button>
           </div>
         </div>
      )}

      <Footer />
    </div>
  )
}