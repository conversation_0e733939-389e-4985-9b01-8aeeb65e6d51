'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { createPortal } from 'react-dom'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useDashboardData, updateFaqCountInCache } from '@/hooks/useOptimizedData'
import { v4 as uuidv4 } from 'uuid'
import { sendFaqUpdateWebhook } from '@/app/api/webhooks/faq'
import { useLanguage } from '@/context/LanguageContext'
import { optimizeGalleryImage } from '@/utils/imageOptimization'
import { FaTrash } from 'react-icons/fa';

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  return (
    <div
      className={`${className} bg-zinc-700 rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 bg-zinc-600 transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-white/5 text-zinc-400 text-xs">
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

// Types for our database records
type FAQ = {
  id: number
  question: string
  answer: string
  question_p?: string
  answer_p?: string
  created_at: string
  client_id: string
  audio_url?: string
  audio_duration?: number
  audio_file_path?: string
  photo_url?: any // JSONB field from database
  photo_id?: string // Product name field
  localAudio?: {
    url: string;
    duration: number;
  };
}

// Define the type for product info similar to the knowledge page
interface ProductInfo {
  prod_id: string;
  name: string;
  photo_url: string | null; // Thumbnail for display
  full_photo_urls: string[] | null; // Full array for saving to DB
}

export default function KnowledgeBasePage() {
  const supabase = createClientComponentClient()
  const { t } = useLanguage()

  // Use dashboard cache for knowledge stats and client info
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo
  const knowledgeStats = dashboardData?.knowledgeStats

  // Get knowledge stats from dashboard cache
  const totalFaqs = knowledgeStats?.faqCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  const isLoadingCount = isDashboardLoading

  // Viewing popup state
  const [viewingItem, setViewingItem] = useState<{field: 'question' | 'answer', value: string} | null>(null)
  const viewModalRef = useRef<HTMLDivElement>(null)

  // Data states
  const [questions, setQuestions] = useState<FAQ[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Audio playback state
  const [playingAudio, setPlayingAudio] = useState<{id: number, audio: HTMLAudioElement, duration: number} | null>(null)
  const [remainingTime, setRemainingTime] = useState<number | null>(null)

  // Virtual scrolling state
  const [visibleStartIndex, setVisibleStartIndex] = useState(0)
  const [visibleEndIndex, setVisibleEndIndex] = useState(14) // Show 15 items initially
  const itemHeight = 120 // Fixed height for each FAQ item
  const visibleItemsCount = 15 // Number of visible items

  // Search state
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredQuestions, setFilteredQuestions] = useState<FAQ[]>([])

  // Recently added state
  const [recentQA, setRecentQA] = useState<(FAQ & {productInfo?: ProductInfo}) | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)

  // Photo search state
  const [selectedProduct, setSelectedProduct] = useState<ProductInfo | null>(null)
  const [photoSearchQuery, setPhotoSearchQuery] = useState('')
  const [isSearchingPhoto, setIsSearchingPhoto] = useState(false)
  const [isPhotoLoading, setIsPhotoLoading] = useState(false)
  const [photoSearchResults, setPhotoSearchResults] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])
  const [showPhotoResults, setShowPhotoResults] = useState(false)

  // Add state to store all photos for local search
  const [allPhotos, setAllPhotos] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])



  // Edit functionality
  const [editingItem, setEditingItem] = useState<{id: number, field: 'question' | 'answer', value: string} | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Track if textarea has been focused for mobile behavior
  const [hasFocusedInput, setHasFocusedInput] = useState(false)

  // State for update status
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const confirmModalRef = useRef<HTMLDivElement>(null)

  // Delete confirmation
  const [deleteConfirm, setDeleteConfirm] = useState<{id: number, isAudio: boolean, audio_file_path?: string} | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const deleteModalRef = useRef<HTMLDivElement>(null)

  // Add a reference for the update section
  const updateSectionRef = useRef<HTMLDivElement>(null)

  // Add state variables for audio deletion and recording
  const [confirmAudioDelete, setConfirmAudioDelete] = useState<number | null>(null)
  const [isRecordingAudio, setIsRecordingAudio] = useState<boolean>(false)
  const [recordingItem, setRecordingItem] = useState<number | null>(null)
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)
  const [isRecording, setIsRecording] = useState(false)
  const [recordingDuration, setRecordingDuration] = useState(0)
  const [recordingTimer, setRecordingTimer] = useState<NodeJS.Timeout | null>(null)
  const [isSavingAudio, setIsSavingAudio] = useState(false)
  const [isProcessingAudio, setIsProcessingAudio] = useState(false); // New state for post-recording processing
  const [newlyRecordedAudio, setNewlyRecordedAudio] = useState<{ id: number; url: string; duration: number; blob: Blob } | null>(null); // Store details of the latest recording
  const [isPlayingNewAudio, setIsPlayingNewAudio] = useState(false);
  const [newAudioRemainingTime, setNewAudioRemainingTime] = useState<number | null>(null);
  const newAudioRef = useRef<HTMLAudioElement | null>(null);
  const audioDeleteModalRef = useRef<HTMLDivElement>(null) // Re-add this ref
  const audioRecordModalRef = useRef<HTMLDivElement>(null) // Re-add this ref

  // Add state variables for local audio blob
  const [localAudioBlob, setLocalAudioBlob] = useState<{ id: number, blob: Blob, url: string, duration: number } | null>(null)

  // Add state for cancel confirmation
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false)
  const cancelConfirmModalRef = useRef<HTMLDivElement>(null)

  // Add state for image gallery
  const [imageGallery, setImageGallery] = useState<{urls: string[], currentIndex: number} | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Add touch support for image gallery
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Add state for dropdown position
  const [dropdownPosition, setDropdownPosition] = useState<{top: number, left: number, width: number} | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // PhotoSearchDropdown component using createPortal
  const PhotoSearchDropdown = ({
    show,
    results,
    position,
    onSelectPhoto
  }: {
    show: boolean,
    results: Array<{
      id: number,
      photo_id: string,
      photo_url: string[] | null,
      photo_file_path: string[] | null
    }>,
    position: {top: number, left: number, width: number} | null,
    onSelectPhoto: (photo: any) => void
  }) => {
    if (!show || !position || typeof window === 'undefined') return null;

    return createPortal(
      <div
        className="photo-search-dropdown fixed z-[9999] bg-zinc-800/95 border border-white/10 rounded-xl shadow-2xl max-h-40 overflow-y-auto backdrop-blur-lg"
        style={{
          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.5), 0 8px 10px -6px rgba(0, 0, 0, 0.3)',
          top: `${position.top}px`,
          left: `${position.left}px`,
          width: `${position.width}px`,
          transform: 'translateY(5px)', // Add a small offset for better appearance
          position: 'fixed', // Ensure fixed positioning
          willChange: 'transform', // Optimize for animations/transitions
          overscrollBehavior: 'contain' // Prevent parent scrolling when dropdown is at boundary
        }}
      >
        {results.length > 0 ? (
          results.map(photo => (
            <div
              key={photo.id}
              className="flex items-center gap-3 p-3 hover:bg-white/5 cursor-pointer border-b border-white/5 last:border-0 transition-colors duration-200"
              style={{
                transition: 'all 0.2s ease'
              }}
              onClick={() => onSelectPhoto(photo)}
            >
              {/* Photo Thumbnail - Optimized */}
              <PhotoThumbnail
                photo={photo}
                className="w-10 h-10"
              />
              {/* Photo ID */}
              <div className="flex-1 truncate">
                <p className="text-white truncate">{photo.photo_id}</p>
              </div>
            </div>
          ))
        ) : (
          <div className="p-3 text-zinc-400 text-center">
            No photos found
          </div>
        )}
      </div>,
      document.body
    );
  };

  // Load initial data - only after client info is available
  useEffect(() => {
    if (clientInfo?.client_id) {
      fetchAllPhotos(); // Add fetch for all photos
    }
  }, [clientInfo?.client_id]); // Only run when client info is loaded

  // Helper function to ensure we get a non-null client ID, language, and sector
  const ensureClientInfo = async (): Promise<{clientId: string, lang: string, sector: string | null}> => {
    // Use client info from dashboard cache
    if (clientInfo?.client_id) {
      return {
        clientId: clientInfo.client_id,
        lang: clientInfo.lang || 'en',
        sector: clientInfo.sector
      };
    }

    // If dashboard cache not ready, wait for it or fallback to database
    if (!clientInfo) {
      throw new Error('Client information not available. Please wait for the page to load completely.');
    }

    throw new Error('Client ID not found. Please refresh the page or log in again.');
  }

  // Removed fetchFaqCount - now using dashboard cache

  // Fetch questions from Supabase
  const fetchQuestions = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Get client info from session or database
      const { clientId } = await ensureClientInfo();

      // Now fetch FAQs with essential fields only for better performance
      const { data, error } = await supabase
        .from('faqs')
        .select('id, question_p, answer_p, created_at, updated_at, audio_url, audio_duration, photo_url, photo_id, audio_file_path, client_id')
        .eq('client_id', clientId)
        .order('updated_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Always use question_p and answer_p fields, map to question/answer for UI
      const processedData = data?.map(faq => ({
        ...faq,
        question: faq.question_p || '',
        answer: faq.answer_p || ''
      })) || [];

      setQuestions(processedData);
      setFilteredQuestions(processedData);

      // FAQ count is now managed by dashboard cache
    } catch (err: any) {
      console.error('Error fetching questions:', err);
      setError(err.message || 'Failed to load questions');
    } finally {
      setIsLoading(false);
    }
  }

  // Incremental update functions for better performance
  const updateQuestionInState = (updatedQuestion: any) => {
    setQuestions(prev => prev.map(q => q.id === updatedQuestion.id ? updatedQuestion : q))
    setFilteredQuestions(prev => prev.map(q => q.id === updatedQuestion.id ? updatedQuestion : q))
  }

  const removeQuestionFromState = (questionId: number) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId))
    setFilteredQuestions(prev => prev.filter(q => q.id !== questionId))
  }

  // Client-side search function (no server search needed since we load all data)
  const performClientSearch = useCallback((query: string) => {
    if (!query.trim()) {
      setFilteredQuestions(questions);
      return;
    }

    const queryLower = query.toLowerCase();
    const filtered = questions.filter(q =>
      q.question.toLowerCase().includes(queryLower) ||
      q.answer.toLowerCase().includes(queryLower)
    );
    setFilteredQuestions(filtered);
  }, [questions]);

  // Load questions only after client info is available
  useEffect(() => {
    if (clientInfo?.client_id) {
      fetchQuestions()
    }
  }, [clientInfo?.client_id])

  // Effect to filter questions based on search query (client-side only)
  useEffect(() => {
    if (!questions.length) return;
    performClientSearch(searchQuery);
  }, [searchQuery, questions, performClientSearch])

  // Calculate visible items for virtual scrolling (now handled by global scroll)



  const handleViewItem = (field: 'question' | 'answer', value: string) => {
    setViewingItem({ field, value })
  }

  // Move item to Recently Added section for editing
  const handleMoveToRecentlyAdded = (qa: FAQ) => {
    // Clear existing items and add just this one
    // If audio_url exists, set answer to empty string (audio and text answers cannot coexist)
    setRecentQA({
      id: qa.id,
      question: qa.question,
      answer: qa.audio_url ? "" : qa.answer, // Set answer to empty string if audio exists
      audio_url: qa.audio_url,
      audio_duration: qa.audio_duration,
      audio_file_path: qa.audio_file_path,
      photo_url: qa.photo_url,
      photo_id: qa.photo_id,
      // These are not used in the recentQA context, but we include them to satisfy the type system
      created_at: qa.created_at,
      client_id: qa.client_id
    })

    // If the item has an image, set it as the selected product
    if (qa.photo_url) {
      const imageData = extractImageUrls(qa.photo_url);
      if (imageData.firstUrl) {
        setSelectedProduct({
          prod_id: `img_${qa.id}`,
          name: qa.photo_id || "Attached Image",
          photo_url: imageData.firstUrl,
          full_photo_urls: imageData.allUrls
        });
      }
    } else {
      // Reset selected product if no image
      setSelectedProduct(null);
    }

    // Reset photo search input and results
    setPhotoSearchQuery('');
    setPhotoSearchResults([]);
    setShowPhotoResults(false);

    // Scroll to update section
    setTimeout(() => {
      if (updateSectionRef.current) {
        updateSectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }, 100)
  }



  // Check if there are any changes to the item being edited
  const hasChanges = () => {
    if (!recentQA) return false;

    // Find the original question from the questions array
    const originalQuestion = questions.find(q => q.id === recentQA.id);

    if (!originalQuestion) return false;

    // Special case: If the original has audio and recentQA has empty answer but no changes to audio,
    // this is not a real change (it's just the initial state when editing an audio answer)
    const isInitialAudioState =
      originalQuestion.audio_url &&
      recentQA.audio_url === originalQuestion.audio_url &&
      recentQA.answer === "" &&
      !newlyRecordedAudio &&
      !recentQA.localAudio;

    // Check for text changes, but ignore the special case for audio answers
    const hasTextChanges = !isInitialAudioState && recentQA.answer !== originalQuestion.answer;

    const hasAudioChanges =
      // New recording added
      newlyRecordedAudio !== null ||
      // Currently recording
      isRecording ||
      // Audio deleted (was present in original but not in current)
      (originalQuestion.audio_url && !recentQA.audio_url) ||
      // Local audio added
      recentQA.localAudio !== undefined;

    // Check for photo changes
    const hasPhotoChanges =
      // Photo added or changed
      (selectedProduct !== null &&
        (
          // No photo originally but now has one
          (!originalQuestion.photo_url && selectedProduct) ||
          // Photo changed (different name)
          (originalQuestion.photo_id !== selectedProduct.name)
        )
      ) ||
      // Photo removed
      (originalQuestion.photo_url && !selectedProduct);

    // Determine if any changes have been made
    return hasTextChanges || hasAudioChanges || hasPhotoChanges;
  }

  // Check if the item has a valid answer (either text or audio)
  const hasValidAnswer = () => {
    if (!recentQA) return false;

    // Check if there's a text answer
    const hasTextAnswer = recentQA.answer && recentQA.answer.trim() !== '';

    // Check if there's an audio answer
    const hasAudioAnswer =
      // Existing audio URL
      recentQA.audio_url !== undefined && recentQA.audio_url !== null ||
      // Newly recorded audio
      newlyRecordedAudio !== null ||
      // Local audio
      recentQA.localAudio !== undefined;

    // Return true if either text or audio answer exists
    return hasTextAnswer || hasAudioAnswer;
  }

  // Prompt for confirmation before update
  const handleUpdate = () => {
    if (!recentQA) {
      setUpdateMessage("No questions to update. Add some questions and answers first.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    if (!hasChanges()) {
      setUpdateMessage("No changes detected. Make some changes before updating.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    if (!hasValidAnswer()) {
      setUpdateMessage("Please provide either a text answer or an audio recording.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    setShowConfirmation(true)
  }

  // Save Q&As to Supabase in batches
  const saveQAToSupabase = async () => {
    setShowConfirmation(false)
    setIsUpdating(true)
    setUpdateStatus('loading')
    setUpdateProgress(0)

    try {
      // Get the client info from session storage
      const { clientId, sector, lang } = await ensureClientInfo()

      // We now only have one item to update (recentQA is a single item, not an array)
      if (!recentQA) {
        throw new Error("No question to update")
      }

      const qa = recentQA; // recentQA is now a single item
      let audioUrl = qa.audio_url;
      let audioFilePath = qa.audio_file_path;
      let audioDuration = qa.audio_duration;

      // Handle audio processing if needed
      if (qa.localAudio) {
        // Find corresponding newly recorded audio blob
        const audioData = newlyRecordedAudio && newlyRecordedAudio.id === qa.id ?
          newlyRecordedAudio : null;

        if (audioData) {
          try {
            // Determine MIME type and extension
            const mimeType = audioData.blob.type || 'audio/webm';
            let fileExtension = 'webm'; // Default extension

            // Get proper file extension based on mime type
            const mimeParts = mimeType.split('/');
            if (mimeParts.length > 1) {
              const subType = mimeParts[1].split(';')[0]; // Handle potential codecs string
              if (subType === 'mp4') fileExtension = 'mp4';
              else if (subType === 'webm') fileExtension = 'webm';
              else if (subType === 'opus') fileExtension = 'opus';
              else if (subType === 'ogg') fileExtension = 'oga'; // Changed to oga for consistency
              else fileExtension = subType; // Use subtype if known
            }

            // Generate unique filename using UUID
            const uniqueFileName = `${uuidv4()}.${fileExtension}`;

            // Get userId for the path (needed for storage RLS policies)
            const { data: userData } = await supabase.auth.getUser();
            if (!userData?.user?.id) {
              throw new Error('User not authenticated. Please log in again.');
            }

            // Create file path with userId
            const targetFilePath = `${userData.user.id}/${uniqueFileName}`;

            // Upload the audio file to Supabase Storage with proper options
            const { data: uploadData, error: uploadError } = await supabase
              .storage
              .from('audios')
              .upload(targetFilePath, audioData.blob, {
                cacheControl: '3600',
                contentType: mimeType,
                upsert: false
              });

            if (uploadError) throw uploadError;

            // Get public URL
            const { data: urlData } = supabase
              .storage
              .from('audios')
              .getPublicUrl(uploadData.path);

            // Set the audio URL and path for the database
            audioUrl = urlData.publicUrl;
            audioFilePath = uploadData.path;
            audioDuration = qa.localAudio.duration;
          } catch (error) {
            console.error('Error uploading audio:', error);
            throw error;
          }
        }
      }

      // Handle product image if selected
      let imageUrl = qa.photo_url;
      let imageName = qa.photo_id;

      if (selectedProduct) {
        // Take URLs from selected product and update photo_url field
        imageUrl = selectedProduct.full_photo_urls;
        imageName = selectedProduct.name;
      } else {
        // If no product selected, check if we're removing an existing photo
        const originalQuestion = questions.find(q => q.id === qa.id);
        if (originalQuestion?.photo_url) {
          // Photo is being removed - clear the photo fields
          imageUrl = undefined;
          imageName = undefined;
        }
      }

      // If we're deleting audio (audio_url is undefined but file path exists)
      // Handle this BEFORE the database update
      if (audioUrl === undefined && qa.audio_file_path) {
        try {
          // Remove the old audio file from storage
          const { error: deleteError, data } = await supabase.storage
            .from('audios')
            .remove([qa.audio_file_path]);

          if (deleteError) {
            console.error('Error deleting old audio:', deleteError);
            // Continue execution even if deletion fails
          } else {
            // If deletion successful, set all audio fields to undefined for database update
            audioUrl = undefined;
            audioDuration = undefined;
            audioFilePath = undefined;
          }
        } catch (error) {
          console.error('Error during audio deletion:', error);
          // Continue execution even if deletion fails
        }
      }

      // Prepare update object - always use answer_p field
      const updateData: any = {
        updated_at: new Date(), // Add this to trigger the database update timestamp
        audio_url: audioUrl === undefined ? null : audioUrl, // Convert undefined to null for database
        audio_duration: audioDuration === undefined ? null : audioDuration, // Convert undefined to null for database
        audio_file_path: audioFilePath === undefined ? null : audioFilePath, // Convert undefined to null for database
        photo_url: imageUrl === undefined ? null : imageUrl,
        photo_id: imageName === undefined ? null : imageName
      };

      // Check if we have a text answer or an audio answer
      const hasTextAnswer = qa.answer && qa.answer.trim() !== '';

      // If we have a text answer and there's an existing audio file, we need to delete it
      // because audio and text answers cannot coexist
      if (hasTextAnswer && qa.audio_file_path && !audioUrl) {
        try {
          // Remove the existing audio file from storage
          const { error: deleteError } = await supabase.storage
            .from('audios')
            .remove([qa.audio_file_path]);

          if (deleteError) {
            console.error('Error deleting audio file when switching to text answer:', deleteError);
            // Continue execution even if deletion fails
          }

          // Set all audio fields to undefined for database update
          audioUrl = undefined;
          audioDuration = undefined;
          audioFilePath = undefined;

          // Update the data object with null values for audio fields
          updateData.audio_url = null;
          updateData.audio_duration = null;
          updateData.audio_file_path = null;

        } catch (error) {
          console.error('Error during audio deletion when switching to text answer:', error);
          // Continue execution even if deletion fails
        }
      }

      // Find the original question to compare changes
      const originalQuestion = questions.find(q => q.id === qa.id);

      // Determine what the final text will be (considering audio/text coexistence logic)
      const finalTextAnswer = audioUrl ? "" : qa.answer;
      const originalTextAnswer = originalQuestion?.answer_p || "";

      // Check if we should ignore text changes (both original and current have audio)
      // This handles transcribed text which is backend-only, not user changes
      const shouldIgnoreTextChanges = originalQuestion?.audio_url && audioUrl;

      // Determine if text has changed (ignore transcription-only changes)
      const hasTextChanged = !shouldIgnoreTextChanges && Boolean(
        // Text content changed
        finalTextAnswer !== originalTextAnswer ||
        // Text was added (original had no text but now has text)
        (!originalTextAnswer && finalTextAnswer?.trim()) ||
        // Text was removed (original had text but now empty)
        (originalTextAnswer && !finalTextAnswer?.trim())
      );

      // Determine if audio has changed
      const hasAudioChanged = Boolean(
        // New recording added
        newlyRecordedAudio !== null ||
        // Audio deleted (was present in original but not in current)
        (originalQuestion?.audio_url && audioUrl === undefined) ||
        // Audio added (wasn't present in original but exists now)
        (!originalQuestion?.audio_url && audioUrl) ||
        // Local audio added
        recentQA?.localAudio !== undefined
      );

      // Only update answer_p if text or audio actually changed
      if (hasTextChanged || hasAudioChanged) {
        if (audioUrl) {
          // If audio URL exists, set the text answer to empty string
          // Audio and text answers cannot coexist
          updateData.answer_p = "";
        } else {
          // Update the answer_p field with text
          updateData.answer_p = qa.answer;
        }
      }

      // Determine if photo has changed
      const hasPhotoChanged =
        // Photo added (didn't exist before but exists now)
        (selectedProduct && !originalQuestion?.photo_url) ||
        // Photo removed (existed before but not now)
        (!selectedProduct && originalQuestion?.photo_url) ||
        // Photo changed (different photo ID)
        (selectedProduct && originalQuestion?.photo_url &&
         originalQuestion?.photo_id !== selectedProduct.name);



      // Handle ATM ID fields based on what changed
      if (hasAudioChanged && hasPhotoChanged) {
        // Both audio and photo changed: clear all ATM IDs
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
        updateData.fb_audio_atmid = null;
        updateData.ig_audio_atmid = null;
        updateData.tg_audio_atmid = null;
      } else if (hasAudioChanged) {
        // Only audio changed: clear audio ATM IDs
        updateData.fb_audio_atmid = null;
        updateData.ig_audio_atmid = null;
        updateData.tg_audio_atmid = null;
      } else if (hasPhotoChanged) {
        // Only photo changed: clear photo ATM IDs
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
      }

      // Update the existing question
      const { error } = await supabase
        .from('faqs')
        .update(updateData)
        .eq('id', qa.id)

      if (error) throw error;

      // Set progress to 100%
      setUpdateProgress(100);



      // Determine if we should call webhook
      // Only skip webhook if ONLY photo changed (no audio or text changes)
      const shouldCallWebhook = !(hasPhotoChanged && !hasAudioChanged && !hasTextChanged);

      if (shouldCallWebhook) {
        // Ensure answer is clean text (no photo URLs or objects)
        const cleanAnswer = typeof qa.answer === 'string' ? qa.answer : '';

        // Send webhook with audio change indicator
        sendFaqUpdateWebhook({
          faq_id: qa.id.toString(), // Convert to string as expected by the webhook
          client_id: qa.client_id,
          answer: cleanAnswer, // Send clean answer text only
          sector: sector,
          lang: lang || 'en', // Send language so webhook knows which fields were updated
          audio_url: audioUrl || "", // Send empty string if no audio URL
          is_audio: hasAudioChanged // Boolean indicating if audio was updated
        });
      }

      // Use incremental update instead of refetching all data
      const updatedQuestion = {
        ...qa,
        question: qa.question,
        answer: finalTextAnswer,
        audio_url: audioUrl,
        audio_duration: audioDuration,
        audio_file_path: audioFilePath,
        photo_url: imageUrl,
        photo_id: imageName,
        updated_at: new Date().toISOString()
      };
      updateQuestionInState(updatedQuestion);

      // Update FAQ count in dashboard cache immediately
      // Since this is an update operation, the count doesn't change
      // But we refresh to ensure consistency
      updateFaqCountInCache(totalFaqs);

      setUpdateStatus('success')
      setUpdateMessage('Successfully updated question to knowledge base.')

      // Clean up object URLs
      if (newlyRecordedAudio) {
        URL.revokeObjectURL(newlyRecordedAudio.url);
      }

      // Clear the recently added item after successful update
      setRecentQA(null)
      setNewlyRecordedAudio(null);
      setSelectedProduct(null);

      // Reset status after a delay
      setTimeout(() => {
        setUpdateStatus('idle')
        setIsUpdating(false)
      }, 1500)

    } catch (err: any) {
      console.error('Error updating knowledge base:', err)
      setUpdateStatus('error')
      setUpdateMessage(err.message || 'Failed to update knowledge base. Please try again.')
      setIsUpdating(false)
    }
  }

  // Helper function to extract URLs from the photo_url JSONB field
  const extractImageUrls = (imageData: any): {firstUrl: string | null, allUrls: string[]} => {
    if (!imageData) {
      return { firstUrl: null, allUrls: [] };
    }

    // If imageData is already a string, return it as the only URL
    if (typeof imageData === 'string') {
      return { firstUrl: imageData, allUrls: [imageData] };
    }

    // Handle case where imageData is an array of URLs
    if (Array.isArray(imageData)) {
      return {
        firstUrl: imageData.length > 0 ? imageData[0] : null,
        allUrls: imageData
      };
    }

    // Handle case where imageData has a url property and perhaps full_urls array
    if (imageData.url) {
      return {
        firstUrl: imageData.url,
        allUrls: imageData.full_urls || [imageData.url]
      };
    }

    // Handle case where imageData has just the full_urls array
    if (imageData.full_urls && Array.isArray(imageData.full_urls)) {
      return {
        firstUrl: imageData.full_urls.length > 0 ? imageData.full_urls[0] : null,
        allUrls: imageData.full_urls
      };
    }

    // Last attempt - try to find any string property that might be a URL
    const possibleUrls = Object.values(imageData).filter(val =>
      typeof val === 'string' && (val.startsWith('http') || val.startsWith('/'))
    ) as string[];

    return {
      firstUrl: possibleUrls.length > 0 ? possibleUrls[0] : null,
      allUrls: possibleUrls
    };
  }

  // Handle delete from Recently Added (just removes from recent items, not from database)
  const handleDelete = () => {
    if (!recentQA) return;

    // Find the original question from the questions array
    const originalQuestion = questions.find(q => q.id === recentQA.id);

    if (!originalQuestion) {
      // If we can't find the original (unlikely), just close the edit view
      setRecentQA(null);
      return;
    }

    // Check for all types of changes
    const hasTextChanges = recentQA.answer !== originalQuestion.answer;
    const hasAudioChanges =
      // New recording added
      newlyRecordedAudio !== null ||
      // Currently recording
      isRecording ||
      // Audio deleted (was present in original but not in current)
      (originalQuestion.audio_url && !recentQA.audio_url) ||
      // Local audio added
      recentQA.localAudio !== undefined;

    // Check for photo changes
    const hasPhotoChanges = selectedProduct !== null &&
      (
        // No photo originally but now has one
        (!originalQuestion.photo_url && selectedProduct) ||
        // Photo changed (different name)
        (originalQuestion.photo_id !== selectedProduct.name)
      );

    // Currently editing text
    const isCurrentlyEditing = editingItem !== null;

    // Determine if any changes have been made
    const hasChanges = hasTextChanges || hasAudioChanges || hasPhotoChanges || isCurrentlyEditing;

    if (hasChanges) {
      // Show confirmation before canceling
      setShowCancelConfirmation(true);
    } else {
      // No changes, proceed with delete
      setRecentQA(null);
    }
  }

  // Handle cancel confirmation - proceed with deletion
  const confirmCancel = () => {
    setShowCancelConfirmation(false);
    setRecentQA(null);

    // Also clean up any ongoing operations
    if (isRecording) {
      cancelRecording();
    }
    if (newlyRecordedAudio) {
      URL.revokeObjectURL(newlyRecordedAudio.url);
      setNewlyRecordedAudio(null);
    }
    if (editingItem) {
      setEditingItem(null);
    }
  }



  // Handle removing image from an item in the update section
  const handleRemoveImage = () => {
    setRecentQA(null)
  }

  // Show delete confirmation
  const showDeleteConfirmation = (id: number, isAudio: boolean, audio_file_path?: string) => {
    setDeleteConfirm({ id, isAudio, audio_file_path })
  }

  // Handle delete from database
  const handleDeleteFromDatabase = async () => {
    if (!deleteConfirm) return

    setIsDeleting(true)

    try {
      const { id, isAudio, audio_file_path } = deleteConfirm

      // If it's an audio entry, delete the audio file from storage first
      if (isAudio && audio_file_path) {
        const { error: storageError } = await supabase
          .storage
          .from('audios')
          .remove([audio_file_path])

        if (storageError) {
          console.error('Error deleting audio file:', storageError)
          throw new Error('Failed to delete audio file. Please try again.')
        }
      }

      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Delete the database record
      const { error: dbError } = await supabase
        .from('faqs')
        .delete()
        .eq('id', id)

      if (dbError) {
        console.error('Error deleting FAQ entry:', dbError)
        throw new Error('Failed to delete question. Please try again.')
      }

      // Use incremental update instead of refetching all data
      removeQuestionFromState(id);

      // Update FAQ count in dashboard cache immediately
      // Decrease count by 1 since we deleted one FAQ
      const currentCount = questions.length; // This is the count before deletion
      const newFaqCount = Math.max(currentCount - 1, 0);
      updateFaqCountInCache(newFaqCount);

      // Update success message
      setUpdateStatus('success')
      setUpdateMessage('Question deleted successfully.')

      // Reset status after a delay
      setTimeout(() => {
        setUpdateStatus('idle')
      }, 1500)

    } catch (error: any) {
      console.error('Error in deletion:', error)
      setUpdateStatus('error')
      setUpdateMessage(error.message || 'Failed to delete. Please try again.')
    } finally {
      setIsDeleting(false)
      setDeleteConfirm(null)
    }
  }



  // Refs for virtual scrolling and navigation
  const knowledgeBaseRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const databaseSectionRef = useRef<HTMLDivElement>(null)

  // Global scroll handler for virtual scrolling
  const handleGlobalScroll = useCallback(() => {
    if (!knowledgeBaseRef.current || filteredQuestions.length === 0) return

    const knowledgeBaseRect = knowledgeBaseRef.current.getBoundingClientRect()
    const viewportHeight = window.innerHeight

    // Check if the knowledge base section is visible in viewport
    if (knowledgeBaseRect.bottom < 0 || knowledgeBaseRect.top > viewportHeight) {
      return // Knowledge base is not visible, no need to update
    }

    // Calculate the content area within the knowledge base (accounting for padding and headers)
    const contentStartY = knowledgeBaseRect.top + 120 // Account for section padding and headers
    const viewportTop = 0
    const viewportBottom = viewportHeight

    // Calculate which items should be visible based on viewport intersection
    // An item is visible if any part of it intersects with the viewport
    let newStartIndex = 0
    let newEndIndex = Math.min(visibleItemsCount - 1, filteredQuestions.length - 1)

    // Only calculate virtual scrolling if content area is partially visible
    if (contentStartY < viewportBottom) {
      // Calculate scroll offset relative to the content start
      const scrollOffset = Math.max(0, viewportTop - contentStartY)

      // Calculate start index - show item if any part is visible (not completely scrolled out)
      newStartIndex = Math.max(0, Math.floor(scrollOffset / itemHeight))

      // Calculate end index - show enough items to fill viewport plus buffer
      const visibleHeight = Math.min(viewportBottom - Math.max(viewportTop, contentStartY), knowledgeBaseRect.bottom - Math.max(viewportTop, contentStartY))
      const itemsNeeded = Math.ceil(visibleHeight / itemHeight) + 2 // +2 for buffer
      newEndIndex = Math.min(newStartIndex + Math.max(itemsNeeded, visibleItemsCount) - 1, filteredQuestions.length - 1)
    }

    setVisibleStartIndex(newStartIndex)
    setVisibleEndIndex(newEndIndex)
  }, [filteredQuestions.length, itemHeight, visibleItemsCount])

  // Effect to add/remove global scroll listener
  useEffect(() => {
    window.addEventListener('scroll', handleGlobalScroll, { passive: true })
    window.addEventListener('resize', handleGlobalScroll, { passive: true })

    // Initial calculation
    handleGlobalScroll()

    return () => {
      window.removeEventListener('scroll', handleGlobalScroll)
      window.removeEventListener('resize', handleGlobalScroll)
    }
  }, [handleGlobalScroll])

  // Effect to reset virtual scroll when search changes
  useEffect(() => {
    setVisibleStartIndex(0)
    setVisibleEndIndex(Math.min(visibleItemsCount - 1, filteredQuestions.length - 1))
    // Trigger recalculation after state update
    setTimeout(handleGlobalScroll, 0)
  }, [searchQuery, filteredQuestions.length, handleGlobalScroll, visibleItemsCount])

  // Scroll to database section
  const scrollToDatabase = () => {
    if (databaseSectionRef.current) {
      databaseSectionRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  // Virtual scrolling helper functions
  const getItemStyle = (index: number) => ({
    position: 'absolute' as const,
    top: index * itemHeight,
    left: 0,
    right: 0,
    height: itemHeight,
  })

  const getTotalHeight = () => filteredQuestions.length * itemHeight

  // Format date string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
  }

  // Add effect to focus textarea and set cursor at the end when editing
  useEffect(() => {
    if (editingItem && textareaRef.current && !hasFocusedInput) {
      // Check if device is desktop (not mobile/tablet) for auto-focus behavior
      const isDesktop = window.innerWidth >= 1024 && !('ontouchstart' in window)

      if (isDesktop) {
        // On desktop: auto-focus and set cursor at the end
        textareaRef.current.focus()
        const length = textareaRef.current.value.length
        textareaRef.current.setSelectionRange(length, length)
        setHasFocusedInput(true)
      }
      // On mobile/tablet: let user manually tap to position cursor and handle keyboard
      // Don't set hasFocusedInput to true yet - wait for user's first click
    }
  }, [editingItem, hasFocusedInput])

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  const handleStartEdit = (id: number, field: 'question' | 'answer', value: string) => {
    setEditingItem({ id, field, value })
    setHasFocusedInput(false)
    // Focus and set cursor at end will happen in useEffect
  }

  const handleSaveEdit = () => {
    if (editingItem) {
      // Always save the edit, even if field is empty
      if (recentQA) {
        setRecentQA({
          ...recentQA,
          [editingItem.field]: editingItem.value
        });
      }
    }
    setEditingItem(null)
  }

  // Handle audio playback with play/pause functionality
  const handleAudioPlayback = (id: number, audioUrl?: string, audioDuration?: number) => {
    if (!audioUrl) return;

    // If already playing this audio, pause it
    if (playingAudio?.id === id) {
      playingAudio.audio.pause();
      setPlayingAudio(null);
      setRemainingTime(null);
      return;
    }

    // If playing different audio, stop it first
    if (playingAudio) {
      playingAudio.audio.pause();
      setRemainingTime(null);
    }

    // Create and play new audio
    const audio = new Audio(audioUrl);

    // Get duration in seconds (already an integer)
    const durationInSeconds = audioDuration || 0;

    // Add event listener to reset state when audio ends
    audio.addEventListener('ended', () => {
      setPlayingAudio(null);
      setRemainingTime(null);
    });

    // Set up time update event
    audio.addEventListener('timeupdate', () => {
      const remaining = durationInSeconds - Math.floor(audio.currentTime);
      setRemainingTime(remaining > 0 ? remaining : 0);
    });

    // Play the audio
    audio.play().catch(error => {
      console.error("Audio playback failed:", error);
    });

    // Set as currently playing
    setPlayingAudio({ id, audio, duration: durationInSeconds });
    setRemainingTime(durationInSeconds);
  };

  // Add function to clear selected product
  const handleClearSelectedProduct = () => {
    setSelectedProduct(null)
  }

  // Add function to fetch all photos
  const fetchAllPhotos = async () => {
    try {
      // Wait for dashboard data to load before fetching photos
      if (!clientInfo?.client_id) {
        return
      }

      const clientId = clientInfo.client_id

      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .order('updated_at', { ascending: false })

      if (error) {
        console.error('Error fetching photos:', error)
        return
      }

      setAllPhotos(data || [])
    } catch (error) {
      console.error('Error in fetchAllPhotos:', error)
    }
  }

  // Add function to search photos from Supabase
  const searchPhotos = async (query: string) => {
    if (!query.trim()) {
      setPhotoSearchResults([])
      setShowPhotoResults(false)
      return
    }

    setIsSearchingPhoto(true)
    try {
      // First try to search locally
      if (allPhotos.length > 0) {
        const filteredPhotos = allPhotos.filter(photo =>
          photo.photo_id.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5)

        if (filteredPhotos.length > 0) {
          setPhotoSearchResults(filteredPhotos)
          setShowPhotoResults(true)
          setIsSearchingPhoto(false)
          return
        }
      }

      // If no local results or no local data, fetch from server
      const { clientId } = await ensureClientInfo()

      // Search photos that match the query by photo_id
      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .ilike('photo_id', `%${query}%`)
        .limit(5)

      if (error) {
        console.error('Error searching photos:', error)
        return
      }

      setPhotoSearchResults(data || [])
      setShowPhotoResults(data && data.length > 0)
    } catch (error) {
      console.error('Error in searchPhotos:', error)
    } finally {
      setIsSearchingPhoto(false)
    }
  }

  // Add function to handle photo search input
  const handlePhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setPhotoSearchQuery(query)
    searchPhotos(query)

    // Update dropdown position immediately when typing
    if (searchInputRef.current && query.trim() !== '') {
      const rect = searchInputRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom,
        left: rect.left,
        width: rect.width
      });
    }
  }

  // Add function to select a photo
  const handleSelectPhoto = (photo: {
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null

    // Show loading animation
    setIsPhotoLoading(true)

    // Hide dropdown and clear search query immediately
    setShowPhotoResults(false)
    setPhotoSearchQuery('')

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedProduct({
        prod_id: `photo_${photo.id}`,
        name: photo.photo_id,
        photo_url: thumbnail,
        full_photo_urls: photo.photo_url
      })

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsPhotoLoading(false)
      }, 50)
    }, 100)
  }



  // Update dropdown position when showing results - keep it attached to search input
  useEffect(() => {
    if (showPhotoResults && searchInputRef.current) {
      const updatePosition = () => {
        const rect = searchInputRef.current!.getBoundingClientRect();
        setDropdownPosition({
          // Use viewport coordinates to keep dropdown attached to input when scrolling
          top: rect.bottom,
          left: rect.left,
          width: rect.width
        });
      };

      updatePosition();

      // Update position on scroll and resize to keep dropdown attached to input
      window.addEventListener('scroll', updatePosition, { passive: true });
      window.addEventListener('resize', updatePosition);

      // Add touchmove event for better mobile support
      document.addEventListener('touchmove', updatePosition, { passive: true });

      // Use requestAnimationFrame for smoother updates during scroll
      let ticking = false;
      const scrollHandler = () => {
        if (!ticking) {
          window.requestAnimationFrame(() => {
            updatePosition();
            ticking = false;
          });
          ticking = true;
        }
      };
      window.addEventListener('scroll', scrollHandler, { passive: true });

      return () => {
        window.removeEventListener('scroll', updatePosition);
        window.removeEventListener('resize', updatePosition);
        document.removeEventListener('touchmove', updatePosition);
        window.removeEventListener('scroll', scrollHandler);
      };
    }
  }, [showPhotoResults]);

  // Close photo search results when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // Check if the click is outside both the search input and the dropdown
      if (
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node) &&
        !(event.target as Element).closest('.photo-search-dropdown')
      ) {
        setShowPhotoResults(false);
      }
    }

    if (showPhotoResults) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPhotoResults])

  // Handle audio delete confirmation
  const handleConfirmAudioDelete = (qaId: number) => {
    setConfirmAudioDelete(qaId)
  }

  // Handle audio delete
  const handleDeleteAudio = () => {
    if (confirmAudioDelete === null) {
      return;
    }

    setRecentQA(prev => {
      if (!prev || prev.id !== confirmAudioDelete) return prev;

      // Create a copy of prev with the same type
      const updatedQA = { ...prev };
      // Modify properties - using undefined instead of null for type compatibility
      updatedQA.audio_url = undefined;
      updatedQA.audio_duration = undefined;
      updatedQA.localAudio = undefined;

      return updatedQA;
    });

    setConfirmAudioDelete(null);

    // If the deleted audio is the newly recorded one, clear that record too
    if (newlyRecordedAudio && newlyRecordedAudio.id === confirmAudioDelete) {
      // Revoke the object URL to prevent memory leaks
      URL.revokeObjectURL(newlyRecordedAudio.url);
      setNewlyRecordedAudio(null);
    }
  }

  // Handle start recording for a specific item
  const startRecordingForItem = (qaId: number) => {
    setRecordingItem(qaId) // Set the ID of the item we're recording for
    setIsRecordingAudio(true) // Show the modal
    setRecordingDuration(0) // Reset duration counter to 0 whenever modal is opened
    // Don't automatically start recording
  }

  const startRecording = async () => {
    if (isRecording) return; // Don't start if already recording

    // Cancel any existing playback of newly recorded audio
    if (isPlayingNewAudio && newAudioRef.current) {
      newAudioRef.current.pause();
      setIsPlayingNewAudio(false);
      setNewAudioRemainingTime(null);
    }

    try {
      // Clear previous recording data if any
      if (newlyRecordedAudio) {
        URL.revokeObjectURL(newlyRecordedAudio.url);
        setNewlyRecordedAudio(null);
      }

      // Reset timer counter to 0
      setRecordingDuration(0);

      // Enhanced audio constraints for better quality (copied from knowledge/page.tsx)
      const audioConstraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          sampleSize: 16,
          channelCount: 1  // Mono for speech clarity
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);
      setAudioStream(stream);

      // Prefer high-quality codec options (copied from knowledge/page.tsx)
      const supportedTypes = [
        'audio/mp4;codecs=mp4a.40.2', // AAC encoding
        'audio/mp4',
        'audio/webm;codecs=opus', // Best quality for voice
        'audio/webm',
      ];

      let selectedMimeType = '';
      for (const type of supportedTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          selectedMimeType = type;
          break;
        }
      }

      if (!selectedMimeType) {
        console.error("[startRecording] No supported mimeType found for MediaRecorder!");
        alert("Your browser doesn't support audio recording in a compatible format.");
        stream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
        return;
      }

      const chunks: Blob[] = [];

      // Set higher bitrate for better quality (copied from knowledge/page.tsx)
      const recorderOptions = {
        mimeType: selectedMimeType,
        audioBitsPerSecond: 128000  // 128kbps for good voice quality
      };

      const recorder = new MediaRecorder(stream, recorderOptions);
      setMediaRecorder(recorder);

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      recorder.onstop = () => {

        if (recordingItem === null) {
          console.error("Recording stopped but no recordingItem ID was set.");
          // Clean up stream just in case
          if (stream) {
            stream.getTracks().forEach(track => track.stop());
            setAudioStream(null);
          }

          // Close modal even if there was an error
          setTimeout(() => {
            setIsProcessingAudio(false);
            setIsRecordingAudio(false);
          }, 1000);

          return; // Exit if we don't know which item this recording belongs to
        }

        // Use the same selectedMimeType when creating the Blob
        const audioBlob = new Blob(chunks, { type: selectedMimeType });

        if (audioBlob.size === 0) {
          console.error("Created audio blob is empty!");
          alert("Failed to record audio. Please try again.");

          // Close modal after a delay
          setTimeout(() => {
            setIsProcessingAudio(false);
            setIsRecordingAudio(false);
          }, 1000);
          return;
        }

        const audioUrl = URL.createObjectURL(audioBlob);

        // Get the final recording duration - try to get it from the mediaRecorder property we set
        // or fall back to the current recordingDuration state
        const finalDuration = (recorder as any).finalRecordingDuration || Math.max(recordingDuration, 1);

        // Test the audio by playing it silently to ensure it works
        const testAudio = new Audio(audioUrl);
        testAudio.volume = 0.01; // Very low volume for testing
        testAudio.onloadedmetadata = () => {
          testAudio.pause();
        };
        testAudio.onerror = (e) => {
          console.error("Error loading test audio:", e);
        };
        testAudio.play().catch(e => {
          console.warn("Could not auto-play test audio (expected):", e);
        });

        // Update the specific QA item with the new local audio URL and duration
        if (recordingItem) {
          setRecentQA(prev => {
            if (!prev) return prev;

            return {
              ...prev,
              localAudio: { url: audioUrl, duration: finalDuration },
              answer: "" // Use an empty answer string to avoid displaying redundant text
            };
          });
        }

        // Store the newly recorded audio details separately for immediate playback/management
        const newAudio = { id: recordingItem, url: audioUrl, duration: finalDuration, blob: audioBlob };
        setNewlyRecordedAudio(newAudio);

        // Clean up stream
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
          setAudioStream(null);
        }

        // Clear recording state but keep track of the item ID
        setIsRecording(false);
        if (recordingTimer) clearInterval(recordingTimer);
        setRecordingTimer(null);

        // Keep isProcessingAudio true during the delay
        // Close modal after a delay to show completion
        setTimeout(() => {
          setIsProcessingAudio(false); // Set this to false just before closing the modal
          setIsRecordingAudio(false); // This will close the modal
        }, 1000);
      };

      recorder.start();
      setIsRecording(true);

      // Stop any existing timer first
      if (recordingTimer) {
        clearInterval(recordingTimer);
      }

      // Start new timer
      const timer = setInterval(() => {
        setRecordingDuration(prev => {
          // Update the timer by incrementing previous value
          const newDuration = prev + 1;
          return newDuration;
        });
      }, 1000);
      setRecordingTimer(timer);

    } catch (error) {
      console.error('Error accessing microphone or starting recording:', error);
      alert('Unable to access microphone. Please check browser permissions.');
      // Ensure cleanup if error occurs during setup
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
      }
      setIsRecording(false);
      setRecordingItem(null);
      if (recordingTimer) clearInterval(recordingTimer);
      setRecordingTimer(null);
    }
  };

  const stopRecording = () => {

    // Immediately capture the current duration before stopping
    const capturedDuration = recordingDuration;

    if (mediaRecorder && isRecording) {
      setIsProcessingAudio(true); // Indicate processing is starting

      try {
        // Stop any existing timer to prevent duration from increasing
        if (recordingTimer) {
          clearInterval(recordingTimer);
          setRecordingTimer(null);
        }

        // Store the final duration in a ref to make sure we can access it in onstop handler
        const finalDuration = Math.max(capturedDuration, 1);

        // Expose duration on mediaRecorder to access it in onstop
        (mediaRecorder as any).finalRecordingDuration = finalDuration;

        mediaRecorder.stop();
        // The onstop handler will manage the rest: blob creation, state updates, and modal closing
      } catch (error) {
        console.error("Error stopping recorder:", error);
        // Fallback in case of error - close modal after delay
        setTimeout(() => {
          setIsProcessingAudio(false);
          setIsRecordingAudio(false);
        }, 1000);
      }
    } else {
      console.warn("stopRecording called but no active recorder or not recording.");
      // Force cleanup if something went wrong
      cancelRecording();
    }
  };

  // Cancel recording
  const cancelRecording = () => {
    // Stop recording if active
    if (mediaRecorder && isRecording) {
      try {
        mediaRecorder.stop();
      } catch (e) {
        console.error("Error stopping media recorder during cancel:", e);
      }

      // Stop all audio tracks
      if (audioStream) {
        audioStream.getAudioTracks().forEach(track => track.stop());
      }

      // Clear timer
      if (recordingTimer) {
        clearInterval(recordingTimer);
        setRecordingTimer(null);
      }
    }

    // Reset recording state
    setIsRecordingAudio(false);
    setIsProcessingAudio(false);
    setRecordingItem(null);
    setIsRecording(false);
    setRecordingDuration(0);
    setAudioStream(null);
    setMediaRecorder(null);
  };

  // Close audio recording modal when clicking outside - DISABLED per request
  /*
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (audioRecordModalRef.current && !audioRecordModalRef.current.contains(event.target as Node)) {
        cancelRecording()
      }
    }

    if (isRecordingAudio) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isRecordingAudio])
  */

  // When component unmounts, clean up any object URLs
  useEffect(() => {
    return () => {
      if (localAudioBlob) {
        URL.revokeObjectURL(localAudioBlob.url)
      }
    }
  }, [])

  // Handle playback for *newly recorded* audio (before saving)
  const handleNewAudioPlayback = useCallback((id: number, audioUrl: string, audioDuration: number) => {
    if (!audioUrl) {
      console.error("No audio URL provided for playback");
      return;
    }

    // If already playing this new audio, pause it
    if (newAudioRef.current && isPlayingNewAudio && newlyRecordedAudio?.id === id) {
      newAudioRef.current.pause();
      setIsPlayingNewAudio(false);
      setNewAudioRemainingTime(null);
      newAudioRef.current = null; // Clear ref
      return;
    }

    // If playing different new audio, stop it first
    if (newAudioRef.current) {
      newAudioRef.current.pause();
      newAudioRef.current = null;
    }

    // Stop existing audio playback if it's running
    if (playingAudio) {
      playingAudio.audio.pause();
      setPlayingAudio(null);
      setRemainingTime(null);
    }

    // Create and play new audio
    const audio = new Audio(audioUrl);

    audio.onerror = (e) => {
      console.error("Audio error during playback:", e, audio.error);
      alert("Error playing audio. Please try recording again.");
      setIsPlayingNewAudio(false);
      newAudioRef.current = null;
    };

    newAudioRef.current = audio; // Store ref

    // Make sure we have a valid duration
    const durationInSeconds = audioDuration && audioDuration > 0 ? audioDuration : 0;

    const endedListener = () => {
      setIsPlayingNewAudio(false);
      setNewAudioRemainingTime(null);
      newAudioRef.current = null; // Clear ref on end
    };

    const timeUpdateListener = () => {
      const remaining = durationInSeconds - Math.floor(audio.currentTime);
      setNewAudioRemainingTime(remaining > 0 ? remaining : 0);
    };

    audio.addEventListener('ended', endedListener);
    audio.addEventListener('timeupdate', timeUpdateListener);

    audio.play().then(() => {
      console.log("Audio playback");
    }).catch(error => {
      console.error("New audio playback failed:", error);
      alert("Failed to play audio. Please try recording again.");
      setIsPlayingNewAudio(false); // Reset state on error
      newAudioRef.current = null;
    });

    setIsPlayingNewAudio(true);
    setNewAudioRemainingTime(durationInSeconds);

    // Return cleanup function for useCallback/useEffect
    return () => {
      audio.removeEventListener('ended', endedListener);
      audio.removeEventListener('timeupdate', timeUpdateListener);
      if (audio) {
         audio.pause(); // Ensure pause on cleanup
      }
    };
  }, [newlyRecordedAudio, isPlayingNewAudio, playingAudio]); // Dependencies


  // Effect to clean up the new audio object if the component unmounts or newlyRecordedAudio changes
  useEffect(() => {
    return () => {
      if (newAudioRef.current) {
        newAudioRef.current.pause();
        // No need to remove listeners here, they are tied to the specific audio instance
      }
    };
  }, []); // Empty dependency array for unmount cleanup

  // Add debug logging for tracking audio duration
  useEffect(() => {
    // Log when recordingDuration changes to track its value
    if (isRecording) {
    }

    // Log newlyRecordedAudio when it changes
    if (newlyRecordedAudio) {
    }

    // Log localAudio for current item in recentQA
    if (recentQA && recentQA.localAudio) {
      const qa = recentQA;
      if (qa.localAudio) {
      }
    }
  }, [recordingDuration, newlyRecordedAudio, recentQA, isRecording]);

  // Handle viewing an image in gallery modal
  const handleViewImage = (imageData: any) => {
    const { allUrls } = extractImageUrls(imageData);
    if (allUrls.length > 0) {
      setImageGallery({
        urls: allUrls,
        currentIndex: 0
      });
    } else {
      // Show a notification or alert that there are no images to display
      setUpdateStatus('error');
      setUpdateMessage('No images available for this item.');
      setTimeout(() => setUpdateStatus('idle'), 3000);
    }
  }

  // Keyboard navigation for image gallery (arrow keys and escape)
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (!imageGallery) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        const prevIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
        setImageGallery({ ...imageGallery, currentIndex: prevIndex });
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        const nextIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
        setImageGallery({ ...imageGallery, currentIndex: nextIndex });
      } else if (event.key === 'Escape') {
        event.preventDefault();
        setImageGallery(null);
      }
    }

    if (imageGallery) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    }
  }, [imageGallery]);

  // Escape key functionality for View Modal
  useEffect(() => {
    function handleViewModalKeyDown(event: KeyboardEvent) {
      if (!viewingItem) return;

      if (event.key === 'Escape') {
        event.preventDefault();
        setViewingItem(null);
      }
    }

    if (viewingItem) {
      document.addEventListener('keydown', handleViewModalKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleViewModalKeyDown);
    }
  }, [viewingItem]);

  // Disable page scroll when gallery is open
  useEffect(() => {
    if (imageGallery) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [imageGallery]);

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = viewingItem || editingItem || showConfirmation || deleteConfirm ||
                       updateStatus === 'loading' || updateStatus === 'success' || updateStatus === 'error' ||
                       isRecordingAudio || confirmAudioDelete || showCancelConfirmation;

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [viewingItem, editingItem, showConfirmation, deleteConfirm, updateStatus, isRecordingAudio, confirmAudioDelete, showCancelConfirmation]);

  // Handle touch events for swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    const minSwipeDistance = 50; // Minimum distance in pixels to be considered a swipe
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      // Handle left swipe (next image)
      const nextIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
      setImageGallery({ ...imageGallery, currentIndex: nextIndex });
    }

    if (isRightSwipe) {
      // Handle right swipe (previous image)
      const prevIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
      setImageGallery({ ...imageGallery, currentIndex: prevIndex });
    }

    // Reset touch coordinates
    setTouchStart(null);
    setTouchEnd(null);
  };

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative pb-16">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden"
          >


            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <Link href="/dashboard" className="group">
                <img
                  src="/images/white_tran_logo.svg"
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-2">
              <Link
                href="/dashboard/knowledge"
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-zinc-300 hover:text-white bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-all duration-200 group"
              >
                <svg
                  className="w-4 h-4 transform -translate-x-0.5 group-hover:-translate-x-1 transition-transform"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                {t('business_insight')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* View Modal */}
          {viewingItem && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                ref={viewModalRef}
                className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-lg mx-4 border border-white/20 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >

                <div className="relative z-10">
                {/* Close button (X) in the top-right corner */}
                <button
                  onClick={() => setViewingItem(null)}
                  className="absolute top-0 right-0 p-1.5 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors z-20"
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <h3 className="text-xl font-bold mb-4 font-title text-center text-white/80">
                  {viewingItem.field === 'question' ? t('question') : t('reply')}
                </h3>
                <div className="bg-black/30 border border-white/20 rounded-lg p-4 text-white mb-4 min-h-[150px] max-h-[200px] overflow-y-auto whitespace-pre-wrap" style={{
                  boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}>
                  {/* Only show text value if not viewing an answer with audio */}
                  {!(viewingItem.field === 'answer' && questions.find(q => q.answer === viewingItem.value)?.audio_url) && viewingItem.value}

                  {/* Display audio player if viewing an answer with audio_url */}
                  {viewingItem.field === 'answer' &&
                   questions.find(q => q.answer === viewingItem.value)?.audio_url && (
                    <div className="mt-1 text-white">
                      <div className="flex items-center">
                        <button
                          className="mr-4 p-2 rounded-full bg-zinc-700 hover:bg-zinc-600 transition-colors"
                          onClick={() => {
                            const qa = questions.find(q => q.answer === viewingItem.value);
                            if (qa?.audio_url) {
                              handleAudioPlayback(qa.id, qa.audio_url, qa.audio_duration);
                            }
                          }}
                        >
                          {/* Play/Pause Icon */}
                          {playingAudio?.id === questions.find(q => q.answer === viewingItem.value)?.id ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          )}
                        </button>
                        <span>
                          {t('audio')}: {playingAudio?.id === questions.find(q => q.answer === viewingItem.value)?.id && remainingTime !== null
                            ? `${remainingTime}s`
                            : `${questions.find(q => q.answer === viewingItem.value)?.audio_duration || '0'}s`}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Edit Modal */}
          {editingItem && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                ref={modalRef}
                className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-lg mx-4 border border-white/20 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >

                <div className="relative z-10">
                {/* Close button (X) */}
                <button
                  className="absolute top-0 right-0 p-1 rounded-full bg-black/40 hover:bg-jade-purple text-white/60 hover:text-white border border-white/20 transition-colors"
                  onClick={() => setEditingItem(null)}
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <h3 className="text-xl text-white/80 font-bold mb-4 font-title text-center">
                  {editingItem.field === 'question' ? t('kb_edit_question') : t('edit_reply')}
                </h3>
                <textarea
                  ref={textareaRef}
                  value={editingItem.value}
                  onChange={(e) => setEditingItem({...editingItem, value: e.target.value})}
                  onClick={() => {
                    // On mobile: only position cursor at end on FIRST click (initial focus)
                    // After that, allow free cursor movement
                    if (!hasFocusedInput && textareaRef.current) {
                      const length = textareaRef.current.value.length;
                      textareaRef.current.setSelectionRange(length, length);
                      setHasFocusedInput(true);
                    }
                    // Subsequent clicks: let user position cursor freely (default browser behavior)
                  }}
                  className="w-full bg-black/30 border border-white/20 rounded-lg p-4 text-white mb-4 min-h-[150px] focus:outline-none focus:border-jade-purple"
                  placeholder={editingItem.field === 'question' ? t('enter_question') : t('enter_reply')}
                />
                <div className="flex space-x-3">
                  {/* <button
                    onClick={() => setEditingItem(null)}
                    className="btn-secondary flex-1"
                  >
                    Cancel
                  </button> */}
                  <button
                    onClick={handleSaveEdit}
                    className="bg-jade-purple/50 text-white hover:bg-jade-purple-dark/75 hover:shadow-md hover:bg-jade-purple transition-all duration-200 px-6 py-2 rounded-lg font-medium w-full border border-jade-purple/75"
                 style={{
                   boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                 }}
                  >
                    {t('save')}
                  </button>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Confirmation Modal */}
          {showConfirmation && (
            <div
              className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                ref={confirmModalRef}
                className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-white/20 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >

                <div className="relative z-10">
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {t('change_knowledge_base')}
                </h3>
                <p className="text-zinc-300 mb-6 text-center">
                  {t('change_question_confirmation')}
                </p>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowConfirmation(false)}
                    className="flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={saveQAToSupabase}
                    className="flex-1 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors"
                  >
                    {t('confirm')}
                  </button>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Delete Confirmation Modal */}
          {deleteConfirm && (
            <div
              className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                ref={deleteModalRef}
                className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >

                <div className="relative z-10">
                <div className="flex justify-center mb-4">
                  <div className="bg-red-600 text-white rounded-full p-3">
                    <FaTrash className="w-4 h-4" />
                  </div>
                </div>
                {/* <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {t('kb_delete_question')}
                </h3> */}
                <p className="text-zinc-300 mb-6 text-center">
                  {t('kb_delete_confirmation')}
                </p>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setDeleteConfirm(null)}
                    className="flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
                    disabled={isDeleting}
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={handleDeleteFromDatabase}
                    className="flex-1 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex justify-center items-center"
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        {t('deleting')}
                      </>
                    ) : (
                      t('delete')
                    )}
                  </button>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Update Status Overlay */}
          {updateStatus === 'loading' && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className="bg-white/5 backdrop-blur-sm border border-white/30 rounded-xl p-8 w-full max-w-sm mx-4 flex flex-col items-center"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
              >
                <div className="w-16 h-16 border-4 border-jade-purple border-t-transparent rounded-full animate-spin mb-4"></div>
                <p className="text-white text-lg mb-2">{t('changing_knowledge_base')}</p>
                <p className="text-white font-mono">{updateProgress}%</p>
              </div>
            </div>
          )}

          {/* Success/Error Message */}
          {(updateStatus === 'success' || updateStatus === 'error') && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className={`bg-white/5 backdrop-blur-sm border ${updateStatus === 'success' ? 'border-green-500' : 'border-red-500'} rounded-xl p-8 w-full max-w-sm mx-4 text-center relative`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
              >
                {/* Close button - only shown for error messages */}
                {updateStatus === 'error' && (
                  <button
                    onClick={() => setUpdateStatus('idle')}
                    className="absolute top-0 right-0 p-1 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors"
                    aria-label="Close"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
                <div className={`${updateStatus === 'success' ? 'text-green-500' : 'text-red-500'}`}>
                  {updateStatus === 'success' ? (
                    <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )}
                </div>
                <p className="text-lg font-semibold mb-1">{updateStatus === 'success' ? t('success') : t('error')}</p>
                <p className="text-white">{updateMessage}</p>
              </div>
            </div>
          )}

          {/* Recently Added Section - Only show when recentQA is set */}
          {recentQA && (
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-8 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
              ref={updateSectionRef}
            >

              <div className="relative z-10">
              <div className="flex flex-row justify-between items-center mb-6">
                <div className="flex-1">
                  <h2 className="text-xl font-bold mb-1 font-title">{t('change_button')}</h2>
                  <p className="text-zinc-400 text-sm font-body">
                    {t('change_business_insights')}
                  </p>
                </div>
                <div>
                  <button
                    onClick={handleUpdate}
                    className={`text-white transition-all duration-200 px-6 py-2 text-xs sm:text-base rounded-lg font-medium border border-white/20 ${
                      !hasChanges() || !hasValidAnswer() || isUpdating || !recentQA
                        ? 'bg-zinc-700/50 cursor-not-allowed opacity-60'
                        : 'bg-jade-purple/90 hover:bg-jade-purple-dark hover:shadow-md'
                    }`}
                    // style={{
                    //   boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                    // }}
                    disabled={isUpdating || !recentQA || !hasChanges() || !hasValidAnswer()}
                  >
                    {isUpdating ? t('changing') : t('change_button')}
                  </button>
                </div>
              </div>

              {/* List Items - Moved product search and selection to top */}
              <div className="w-full">
                <div className="space-y-4">

                  {/* Photo Search Bar */}
                  <div className="mb-4 relative">
                    <div className="relative">
                      <input
                        type="text"
                        value={photoSearchQuery}
                        onChange={handlePhotoSearch}
                        onFocus={() => photoSearchResults.length > 0 && setShowPhotoResults(true)}
                        placeholder={t('kb_search_photo')}
                        className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-white/40"
                        style={{
                          boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)',
                          fontSize: '16px' // Prevent auto-zoom on mobile
                        }}
                        ref={searchInputRef}
                        autoComplete="off" // Prevent browser autocomplete from interfering
                        spellCheck="false" // Disable spell checking
                      />
                      {isSearchingPhoto && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      )}
                    </div>

                    {/* Use the Portal-based dropdown component */}
                    <PhotoSearchDropdown
                      show={showPhotoResults}
                      results={photoSearchResults}
                      position={dropdownPosition}
                      onSelectPhoto={handleSelectPhoto}
                    />
                  </div>

                  {/* Selected Photo Display or Loading Animation */}
                  {isPhotoLoading ? (
                    <div className="mb-4 p-4 bg-white/10 border border-white/10 rounded-xl flex items-center justify-center h-16">
                      <div className="flex items-center space-x-3">
                        <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-white/70 text-sm">{t('loading')}</span>
                      </div>
                    </div>
                  ) : selectedProduct && (
                    <div className="mb-4 p-3 bg-white/10 border border-white/10 rounded-xl flex items-center justify-between animate-fadeIn">
                      <div className="flex items-center gap-3">
                        {/* Photo Thumbnail */}
                        <div className="w-10 h-10 bg-black/50 rounded overflow-hidden flex-shrink-0 border border-white/20">
                          {selectedProduct.photo_url ? (
                            <img
                              src={selectedProduct.photo_url}
                              alt={selectedProduct.name}
                              className="w-full h-full object-cover cursor-pointer"
                              onClick={() => handleViewImage(selectedProduct.full_photo_urls)}
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                              }}
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-white/5 text-zinc-300">
                              <span>{t('kb_no_photo')}</span>
                            </div>
                          )}
                        </div>
                        {/* Photo ID */}
                        <div>
                          <p className="text-white">{selectedProduct.name}</p>
                        </div>
                      </div>
                      {/* Remove Button */}
                      <button
                        onClick={handleClearSelectedProduct}
                        className="p-1 rounded-lg bg-white/5 hover:bg-white/10 text-zinc-300 hover:text-white border border-white/10 transition-colors duration-200"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  )}

                  {/* Question Field (Read-only) */}
                  <div className="bg-black/30 border border-white/20 rounded-lg p-4" style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}>
                    <div className="font-medium text-zinc-400 mb-2">{t('question')} <span className='text-sm text-zinc-500'>({t('kb_question_cannot_be_edited')})</span></div>
                    <div
                      className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white filter blur-[0.3px] opacity-70 cursor-pointer hover:bg-black/40 hover:border-white/40 transition-all"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                      onClick={() => handleViewItem('question', recentQA.question)}
                      title="Click to view question"
                    >
                      <div className="truncate break-words">
                        {recentQA.question}
                      </div>
                    </div>
                  </div>

                  {/* Answer Edit Field */}
                  <div className="bg-black/30 border border-white/20 rounded-lg p-4" style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}>
                    <div className="font-medium text-zinc-400 mb-2">{t('reply')}</div>
                    <div className="w-full relative">
                      <div
                        className={`w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white ${!recentQA.audio_url && !recentQA.localAudio ? 'hover:bg-black/40 hover:border-jade-purple cursor-pointer' : ''} transition-all`}
                        style={{
                          boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                        }}
                        onClick={() => !recentQA.audio_url && !recentQA.localAudio && handleStartEdit(recentQA.id, 'answer', recentQA.answer)}
                      >
                        <div className={`${!recentQA.answer && !recentQA.audio_url && !recentQA.localAudio ? 'text-zinc-500' : ''} truncate break-words pr-10`}
                             title={!recentQA.audio_url && !recentQA.localAudio ? t('click_to_edit') : undefined}>
                          {/* Only show text answer if no audio exists */}
                          {!recentQA.audio_url && !recentQA.localAudio ?
                            (recentQA.answer || t('enter_reply')) :
                            ""}
                          {recentQA.localAudio && (
                            <div className="mt-1 text-white flex items-center justify-between">
                              <div className="flex items-center">
                                <button
                                  className="mr-3 hover:opacity-80 focus:outline-none"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // First try using newlyRecordedAudio if it matches this QA item since it's freshest
                                    if (newlyRecordedAudio?.id === recentQA.id) {
                                      handleNewAudioPlayback(recentQA.id, newlyRecordedAudio.url, newlyRecordedAudio.duration);
                                    }
                                    // Fall back to localAudio if available
                                    else if (recentQA.localAudio) {
                                      handleNewAudioPlayback(recentQA.id, recentQA.localAudio.url, recentQA.localAudio.duration);
                                    }
                                  }}
                                  title={isPlayingNewAudio && newlyRecordedAudio?.id === recentQA.id ? t('kb_pause_audio') : t('kb_play_audio')}
                                >
                                  {isPlayingNewAudio && newlyRecordedAudio?.id === recentQA.id ? (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  ) : (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  )}
                                </button>
                                <span className="text-sm">
                                  {t('audio')}: {isPlayingNewAudio && newlyRecordedAudio?.id === recentQA.id && newAudioRemainingTime !== null
                                    ? `${newAudioRemainingTime}s` // Playing - show countdown
                                    : newlyRecordedAudio?.id === recentQA.id
                                      ? `${newlyRecordedAudio.duration}s` // Not playing, use newlyRecordedAudio duration
                                      : recentQA.localAudio?.duration && recentQA.localAudio.duration > 0
                                        ? `${recentQA.localAudio.duration}s` // Use localAudio duration if available
                                        : '1s' // Fallback to 1s if all else fails
                                  }
                                </span>
                              </div>
                              <button
                                className="text-red-500 hover:text-red-700 focus:outline-none absolute right-3"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleConfirmAudioDelete(recentQA.id);
                                }}
                                title={t('kb_delete_audio')}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          )}
                          {recentQA.audio_url && (
                            <div className="mt-1 text-white flex items-center justify-between">
                              <div className="flex items-center">
                                <button
                                  className="mr-3 hover:opacity-80 focus:outline-none"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleAudioPlayback(recentQA.id, recentQA.audio_url, recentQA.audio_duration);
                                  }}
                                  title={playingAudio?.id === recentQA.id ? t('kb_pause_audio') : t('kb_play_audio')}
                                >
                                  {playingAudio?.id === recentQA.id ? (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  ) : (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  )}
                                </button>
                                <span className="text-sm">
                                  {t('audio')}: {playingAudio?.id === recentQA.id && remainingTime !== null
                                    ? `${remainingTime}s`
                                    : `${recentQA.audio_duration || '0'}s`}
                                </span>
                              </div>
                              <button
                                className="text-red-500 hover:text-red-700 focus:outline-none absolute right-3"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleConfirmAudioDelete(recentQA.id);
                                }}
                                title={t('kb_delete_audio')}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Add microphone button when no audio is present */}
                      {!recentQA.audio_url && !recentQA.localAudio && (
                        <button
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1.5 bg-black/30 hover:bg-jade-purple rounded-full transition-colors border border-white/20 hover:border-jade-purple-dark"
                          style={{
                            boxShadow: '0 0 10px rgba(255, 255, 255, 0.1)'
                          }}
                          onClick={() => startRecordingForItem(recentQA.id)}
                          title={t('kb_record_audio_answer')}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Cancel/Close Button */}
                  <div className="flex justify-end">
                    <button
                      className="bg-red-600/80 hover:bg-red-700 text-white transition-all duration-200 px-6 py-2 text-xs sm:text-base rounded-lg font-medium border border-white/20"
                      // style={{
                      //   boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                      // }}
                      onClick={handleDelete}
                    >
                      <span>{t('cancel')}</span>
                    </button>
                  </div>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Knowledge Base Section */}
          <div
            ref={(el) => {
              if (knowledgeBaseRef.current !== el) {
                (knowledgeBaseRef as any).current = el;
              }
              if (databaseSectionRef.current !== el) {
                (databaseSectionRef as any).current = el;
              }
            }}
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-8 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
          >

            <div className="relative z-10">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <div>
                <h2 className="text-xl font-bold mb-1 font-title">{t('kb_title')}</h2>
                <p className="text-zinc-400 text-sm font-body">
                  {t('kb_subtitle')}
                </p>
              </div>
              {/* Only show search when data is loaded */}
              {!isLoading && (
                <div className="mt-4 md:mt-0 w-full md:w-auto">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder={t('kb_search_questions')}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full md:w-64 px-4 py-2 pl-10 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-500 focus:outline-none focus:border-white/40"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                    />
                    <svg className="w-5 h-5 text-zinc-500 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              )}
            </div>

            {/* Loading State */}
            {isLoading && (
              <>
                {/* Loading header */}
                <div className="py-8 flex justify-center items-center">
                  <div className="flex flex-col items-center space-y-4">
                    {/* Main loading spinner */}
                    <div className="relative">
                      <div className="w-12 h-12 border-4 border-jade-purple/20 border-t-jade-purple rounded-full animate-spin"></div>
                      <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-jade-purple/40 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                    </div>

                    {/* Loading text with animation */}
                    <div className="text-center">
                      <p className="text-white text-lg font-medium mb-1">{t('kb_loading_questions')}</p>
                      <p className="text-zinc-400 text-sm">
                        {questions.length > 0
                          ? `${t('kb_loaded')} ${questions.length} ${t('kb_questions')}...`
                          : t('kb_preparing_knowledge_base')
                        }
                      </p>
                    </div>

                    {/* Progress indicator */}
                    <div className="w-48 h-1 bg-zinc-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-jade-purple to-jade-purple-dark rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Skeleton loading for FAQ list */}
                <div className="space-y-3">
                  {/* Column headers skeleton */}
                  <div className="flex border-b border-zinc-800 py-3 font-semibold text-zinc-400 font-body">
                    <div className="w-[6%] px-2"></div>
                    <div className="w-[35%] px-2">
                      <div className="h-4 bg-zinc-700/50 rounded animate-pulse"></div>
                    </div>
                    <div className="w-[35%] px-2">
                      <div className="h-4 bg-zinc-700/50 rounded animate-pulse"></div>
                    </div>
                    <div className="w-[15%] px-2">
                      <div className="h-4 bg-zinc-700/50 rounded animate-pulse"></div>
                    </div>
                    <div className="w-[9%] px-2"></div>
                  </div>

                  {/* Skeleton FAQ rows */}
                  {Array.from({ length: 8 }).map((_, index) => (
                    <div key={index} className="flex border-b border-zinc-800/50 py-3 items-center">
                      <div className="w-[6%] px-2">
                        <div className="h-4 w-6 bg-zinc-700/30 rounded animate-pulse"></div>
                      </div>
                      <div className="w-[35%] px-2">
                        <div className="h-4 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.1}s` }}></div>
                      </div>
                      <div className="w-[35%] px-2">
                        <div className="h-4 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.15}s` }}></div>
                      </div>
                      <div className="w-[15%] px-2">
                        <div className="h-8 w-8 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.2}s` }}></div>
                      </div>
                      <div className="w-[9%] px-2 flex flex-col space-y-1">
                        <div className="h-6 w-6 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.25}s` }}></div>
                        <div className="h-6 w-6 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.3}s` }}></div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}

            {/* Error State */}
            {error && !isLoading && (
              <div className="py-12 text-center text-red-400">
                <p>{error}</p>
                <button
                  onClick={fetchQuestions}
                  className="mt-4 px-4 py-2 bg-zinc-800 hover:bg-zinc-700 rounded-lg text-white"
                >
                  {t('kb_try_again')}
                </button>
              </div>
            )}

            {/* Empty State */}
            {!isLoading && !error && filteredQuestions.length === 0 && (
              <div className="py-12 text-center text-zinc-400">
                {searchQuery ? (
                  <p>{t('kb_no_questions_found')}</p>
                ) : (
                  <p>{t('kb_no_questions_added')}</p>
                )}
              </div>
            )}

            {/* Knowledge Base Content */}
            {!isLoading && !error && filteredQuestions.length > 0 && (
              <>
                {/* Knowledge Base Column Headers */}
                <div className="flex border-b border-zinc-800 py-3 font-semibold text-zinc-400 font-body">
                  <div className="w-[6%] px-2 text-left"></div>
                  <div className="w-[35%] px-2 text-left">{t('kb_question_column')}</div>
                  <div className="w-[35%] px-2 text-left">{t('kb_reply_column')}</div>
                  <div className="w-[15%] px-2 text-left"></div>
                  <div className="w-[9%] px-2 text-center"></div>
                </div>

                {/* Virtual Scrolling Container */}
                <div
                  ref={scrollContainerRef}
                  className="w-full relative"
                >
                  {/* Virtual list container with total height */}
                  <div style={{ height: getTotalHeight(), position: 'relative' }}>
                    {/* Render only visible items */}
                    {filteredQuestions.slice(visibleStartIndex, visibleEndIndex + 1).map((qa, index) => {
                      const actualIndex = visibleStartIndex + index;
                      return (
                        <div
                          key={qa.id}
                          className="flex border-b border-zinc-800 py-3 items-center absolute w-full"
                          style={getItemStyle(actualIndex)}
                        >
                          <div className="w-[6%] px-2 text-left text-zinc-500 font-body">
                            {actualIndex + 1}
                          </div>
                      <div
                        className="w-[35%] px-2 cursor-pointer hover:bg-white/20 rounded py-1 transition-colors"
                        onClick={() => handleViewItem('question', qa.question)}
                      >
                        <div className="truncate break-words" title={qa.question}>
                          {qa.question}
                        </div>
                      </div>
                      <div
                        className="w-[35%] px-2 py-1 rounded transition-colors cursor-pointer hover:bg-white/20"
                        onClick={() => handleViewItem('answer', qa.answer)}
                      >
                        <div className="truncate break-words" title={qa.answer}>
                          {/* Conditional Rendering for Answer/Audio */}
                          {qa.localAudio ? (
                            // Display New Audio Playback UI
                            <div className="mt-1 text-white flex items-center justify-between pr-1">
                              <div className="flex items-center">
                                 <button
                                   className="mr-3 hover:opacity-80 focus:outline-none"
                                   onClick={(e) => {
                                     e.stopPropagation(); // Prevent triggering other clicks
                                     if (qa.localAudio) { // Type guard
                                         handleNewAudioPlayback(qa.id, qa.localAudio.url, qa.localAudio.duration);
                                     }
                                   }}
                                   title={isPlayingNewAudio && newlyRecordedAudio?.id === qa.id ? t('kb_pause_audio') : t('kb_play_recorded_audio')}
                                 >
                                   {/* Play/Pause Icon based on isPlayingNewAudio state */}
                                   {isPlayingNewAudio && newlyRecordedAudio?.id === qa.id ? (
                                     <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                     </svg>
                                   ) : (
                                     <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                     </svg>
                                   )}
                                 </button>
                                 <span className="text-sm">
                                   {t('audio')}: {isPlayingNewAudio && newlyRecordedAudio?.id === qa.id && newAudioRemainingTime !== null
                                     ? `${newAudioRemainingTime}s`
                                     : newlyRecordedAudio?.id === qa.id
                                       ? `${newlyRecordedAudio.duration}s`
                                       : qa.localAudio?.duration && qa.localAudio.duration > 0
                                         ? `${qa.localAudio.duration}s`
                                         : '1s'
                                   }
                                 </span>
                              </div>
                              {/* Optionally add a delete/re-record button here if needed */}
                            </div>
                          ) : qa.audio_url ? (
                            // Display Existing Audio Playback UI (unchanged)
                            // Don't show text answer when audio exists
                            <div className="mt-1 text-white flex items-center justify-between pr-1">
                              {/* ... existing audio playback button and time ... */}
                              <div className="flex items-center">
                                  <button
                                    className="mr-3 hover:opacity-80 focus:outline-none"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleAudioPlayback(qa.id, qa.audio_url, qa.audio_duration);
                                    }}
                                    title={playingAudio?.id === qa.id ? t('kb_pause_audio') : t('kb_play_audio')}
                                  >
                                    {playingAudio?.id === qa.id ? (
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                    ) : (
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                    )}
                                  </button>
                                  <span className="text-sm">
                                     {playingAudio?.id === qa.id && remainingTime !== null
                                      ? `(${remainingTime}s)`
                                      : `(${qa.audio_duration || '0'}s)`}
                                  </span>
                                </div>
                            </div>
                          ) : (
                             // Display Text Answer only
                             qa.answer
                          )}
                        </div>
                      </div>
                      <div className="w-[15%] px-2 py-1">
                        {qa.photo_url && (
                          <div className="h-full w-full px-3 py-1.5 rounded-lg flex flex-col items-center justify-center">
                            <PhotoThumbnail
                              photo={{
                                photo_url: extractImageUrls(qa.photo_url).allUrls || [],
                                photo_id: qa.photo_id || 'FAQ Image'
                              }}
                              className="w-10 h-10 border border-white/20"
                              onClick={() => handleViewImage(qa.photo_url)}
                            />
                          </div>
                        )}
                      </div>
                      <div className="w-[9%] px-2 flex flex-col space-y-1">
                        <button
                          className="text-jade-purple hover:text-jade-purple-dark text-lg p-3"
                          onClick={() => handleMoveToRecentlyAdded(qa)}
                          title={t('kb_edit_question')}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          className="text-red-500 hover:text-red-700 text-lg p-3"
                          onClick={() => showDeleteConfirmation(qa.id, !!qa.audio_url, qa.audio_file_path)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )
                })}
                  </div>
                </div>
              </>
            )}
            </div>
          </div>
        </motion.div>
      </div>

      <Footer />

      {/* Audio Delete Confirmation Modal */}
      {confirmAudioDelete !== null && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            ref={audioDeleteModalRef}
            className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >

            <div className="relative z-10">
            <div className="flex justify-center mb-4">
              <div className="bg-red-600 text-white rounded-full p-3">
                <FaTrash className="w-4 h-4" />
              </div>
            </div>
            {/* <h3 className="text-xl font-bold mb-4 font-title text-center">
              {t('kb_delete_audio_title')}
            </h3> */}
            <p className="text-zinc-300 mb-6 text-center">
              {t('kb_delete_audio_message')}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setConfirmAudioDelete(null)}
                className="flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
              >
                {t('cancel')}
              </button>
              <button
                onClick={handleDeleteAudio}
                className="flex-1 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                {t('delete')}
              </button>
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Audio Recording Modal - Updated */}
      {isRecordingAudio && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            ref={audioRecordModalRef}
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-white/20 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >

            <div className="relative z-10">
            {/* Close button (X) - always visible but behavior changes when recording */}
            <button
              className="absolute top-0 right-0 p-1 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors"
              onClick={isRecording ? stopRecording : cancelRecording}
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <h3 className="text-xl font-bold mb-4 font-title text-center">
              {/* Title changes based on processing state */}
              {isProcessingAudio ? t('kb_processing_audio') : t('kb_record_audio')}
            </h3>
            <div className="flex justify-center items-center mb-6 h-32">
              {isProcessingAudio ? (
                // Show only spinner when processing
                <div className="w-16 h-16 border-4 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
              ) : (
                // Show recording indicator circle otherwise
                <div className={`w-32 h-32 rounded-full flex items-center justify-center ${isRecording ? 'bg-red-500 bg-opacity-20 border-2 border-red-500 animate-pulse' : 'bg-zinc-800 border-2 border-zinc-700'}`}>
                  <div className="text-center">
                    <div className="text-2xl font-mono">
                      {`${recordingDuration}s`}
                    </div>
                    <div className="text-sm text-zinc-400 mt-1">
                      {isRecording ? t('kb_recording') : t('kb_ready')}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Buttons only shown when not processing */}
            {!isProcessingAudio && (
              <div className="flex justify-center">
                {!isRecording ? (
                  <button
                    onClick={startRecording}
                    className="py-2 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors flex items-center justify-center"
                  >
                    {/* Start recording icon */}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                    {t('start_recording')}
                  </button>
                ) : (
                  <button
                    onClick={stopRecording}
                    className="py-2 px-4 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center justify-center"
                  >
                    {/* Stop recording icon */}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                    </svg>
                    {t('stop_recording')}
                  </button>
                )}
              </div>
            )}
            </div>
          </div>
        </div>
      )}

      {/* Cancel Confirmation Modal */}
      {showCancelConfirmation && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            ref={cancelConfirmModalRef}
            className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
          >

            <div className="relative z-10">
            <h3 className="text-xl font-bold mb-4 font-title text-center">
              {t('kb_discard_changes')}
            </h3>
            <p className="text-zinc-300 mb-6 text-center">
              {t('kb_unsaved_changes')}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowCancelConfirmation(false)}
                className="flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
              >
                {t('kb_keep_editing')}
              </button>
              <button
                onClick={confirmCancel}
                className="flex-1 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                {t('kb_discard')}
              </button>
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Gallery Modal */}
      {imageGallery && (
        <div
          className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            ref={imageGalleryRef}
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-3xl mx-4 border border-white/20 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >

            <div className="relative z-10">
            {/* Close button in the top-right corner */}
            <button
              onClick={() => setImageGallery(null)}
              className="absolute top-0 right-0 p-1.5 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors z-20"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="flex flex-col">
              {/* Image counter */}
              <div className="text-center mb-2 text-sm text-zinc-400">
                {imageGallery.currentIndex + 1} / {imageGallery.urls.length}
              </div>

              {/* Main image container with touch events */}
              <div
                className="relative h-[60vh] flex items-center justify-center"
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                aria-live="polite"
                role="region"
                aria-label={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
              >
                {imageGallery.urls.length === 0 ? (
                  <div className="text-center text-zinc-400 p-8 bg-zinc-800/50 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-zinc-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p>{t('no_images_available')}</p>
                  </div>
                ) : (
                  <div className="relative">
                    <img
                      src={optimizeGalleryImage(imageGallery.urls[imageGallery.currentIndex])}
                      alt="Gallery Image"
                      className="rounded-lg max-w-full max-h-full object-contain"
                      loading="eager"
                      decoding="async"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                      }}
                    />
                  </div>
                )}

                {/* Navigation buttons - only show if more than one image */}
                {imageGallery.urls.length > 1 && (
                  <>
                    {/* Previous button */}
                    <button
                      onClick={() => {
                        const prevIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
                        setImageGallery({ ...imageGallery, currentIndex: prevIndex });
                      }}
                      className="absolute left-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label={t('previous_page')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {/* Next button */}
                    <button
                      onClick={() => {
                        const nextIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
                        setImageGallery({ ...imageGallery, currentIndex: nextIndex });
                      }}
                      className="absolute right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label={t('next_page')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}
              </div>

              {/* Thumbnail strip - only show if more than one image */}
              {imageGallery.urls.length > 1 && (
                <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                  {imageGallery.urls.map((url, index) => (
                    <PhotoThumbnail
                      key={index}
                      photo={{
                        photo_url: [url],
                        photo_id: `thumbnail-${index + 1}`
                      }}
                      className={`w-16 h-16 rounded-lg border-2 transition-all duration-200 ease-in-out ${
                        index === imageGallery.currentIndex ? 'border-white/80 scale-105 opacity-100' : 'border-zinc-700 opacity-70 hover:opacity-90 hover:border-zinc-600'
                      }`}
                      onClick={() => {
                        setImageGallery({ ...imageGallery, currentIndex: index });
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
