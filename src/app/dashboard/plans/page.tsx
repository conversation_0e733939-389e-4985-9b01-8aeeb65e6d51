'use client'

import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import { createClientComponentClient } from '@/utils/supabase/client'
import { getClientInfo, ClientInfo } from '@/utils/client'
import { useLanguage } from '@/context/LanguageContext'
import { pricingPlans, billingCycleKeys } from '@/lib/pricing'
import Footer from '@/components/Footer'
import PaymentProcessor from '@/components/PaymentProcessor'

export default function PlansPage() {
  const [billingCycle, setBillingCycle] = useState(1) // 0 = 1 month, 1 = 3 months
  const [isMounted, setIsMounted] = useState(false)
  // We store clientInfo but don't directly use it in the UI
  const [, setClientInfo] = useState<ClientInfo | null>(null)
  const [subscriptionData, setSubscriptionData] = useState<{
    plan_type: string | null;
    next_billing_date: string | null;
  }>({
    plan_type: null,
    next_billing_date: null
  })
  const [showPaymentProcessor, setShowPaymentProcessor] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const { t, language } = useLanguage()
  const supabase = createClientComponentClient()

  // Filter out the add-on plan for the main plans display
  const mainPlans = pricingPlans.filter(plan => !plan.isAddon)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    if (!isMounted) return

    async function loadSubscriptionData() {
      try {
        const cachedClientInfo = getClientInfo()
        setClientInfo(cachedClientInfo)

        if (cachedClientInfo) {
          // Get plan_type and next_billing_date directly from clients table
          const { data: clientData, error: clientError } = await supabase
            .from('clients')
            .select('plan_type, next_billing_date')
            .eq('client_id', cachedClientInfo.client_id)
            .single()

          if (clientError) {
            console.error('Error fetching client data:', clientError)
            setSubscriptionData({ plan_type: null, next_billing_date: null })
          } else if (clientData) {
            setSubscriptionData({
              plan_type: language === 'kh' ? 
                (clientData.plan_type === 'Intern' ? 'អ្នកហាត់ការ' : 
                 clientData.plan_type === 'Assistant' ? 'ជំនួយការ' : 
                 clientData.plan_type === 'Manager' ? 'អ្នកគ្រប់គ្រង' :
                 clientData.plan_type) : clientData.plan_type,
              next_billing_date: clientData.next_billing_date
            })
          }
        }
      } catch (error) {
        console.error('Error loading subscription data:', error)
      }
    }

    loadSubscriptionData()
  }, [isMounted, supabase, language])

  // Format the date to a more readable format
  const formatDate = (dateString: string | null) => {
    if (!dateString) return t('not_available');
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'kh' ? 'km-KH' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      <div className="flex-grow container mx-auto px-4 pt-4 pb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Top navigation-like row with logo */}
          <div className="flex justify-center py-2 mb-8">
            <Link href="/dashboard">
              <img
                src="/images/white_tran_logo.svg"
                alt="Chhlat Logo"
                className="h-10 w-auto cursor-pointer hover:opacity-80 transition-opacity"
              />
            </Link>
          </div>

          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <Link href="/dashboard" className="text-sm text-zinc-400 hover:text-white">
                ← {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title">
                <span className="text-jade-purple">{t('subscription')}</span> {t('plans')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          <div className="relative bg-deep-blue/60 backdrop-blur-lg rounded-2xl p-6 mb-8 hover:border-white/70 transition-all duration-300 overflow-hidden"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.025)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '0.75rem',
              boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            }}
          >
            <div className="absolute inset-0 rounded-xl overflow-hidden" style={{
              pointerEvents: 'none'
            }}></div>
            <div className="absolute inset-0 rounded-xl overflow-hidden opacity-30" style={{
              pointerEvents: 'none'
            }}></div>
            <h2 className="text-xl font-bold mb-4 font-title">{t('current_plan')}</h2>
            <div className="flex items-center">
              <div className="flex-shrink-0 w-16 h-16 bg-jade-purple/20 rounded-full flex items-center justify-center">
                <span className="text-jade-purple text-2xl font-bold">{subscriptionData.plan_type?.charAt(0) || 'N'}</span>
              </div>
              <div className="ml-6">
                <h3 className="text-lg font-semibold">{subscriptionData.plan_type || t('no_plan')}</h3>
                <p className="text-zinc-400 text-sm">
                  {subscriptionData.next_billing_date
                    ? t('renews_on').replace('{date}', formatDate(subscriptionData.next_billing_date))
                    : t('no_billing_info')}
                </p>
              </div>
            </div>
          </div>

          <div className="mb-8 flex justify-center">
            <div className="inline-flex p-2 border-white/30 border rounded-xl" style={{
              backgroundColor: 'rgba(255, 255, 255, 0.025)',
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.15)'
            }}>
              {billingCycleKeys.map((cycleKey, index) => (
                <motion.button
                  key={index}
                  className={`py-3 px-6 text-base font-medium font-body ${
                    billingCycle === index
                      ? 'btn-glass-filled-flat'
                      : 'btn-glass-outline-flat'
                  }`}
                  style={{
                    paddingTop: '0.75rem',
                    paddingBottom: '0.75rem',
                    paddingLeft: '1.5rem',
                    paddingRight: '1.5rem',
                    marginRight: '0.25rem',
                    marginLeft: '0.25rem'
                  }}
                  onClick={() => setBillingCycle(index)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  {t(cycleKey)}
                </motion.button>
              ))}
            </div>
          </div>

          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6 px-6 sm:px-0"
            variants={container}
            initial="hidden"
            animate="show"
          >
            {mainPlans.map((plan, index) => (
              <motion.div
                key={index}
                className={`card overflow-hidden flex flex-col ${
                  plan.isPopular
                    ? 'relative'
                    : ''
                }`}
                style={plan.isPopular ? {
                  borderColor: 'rgba(134, 107, 255, 0.25)',
                  border: '1px solid rgba(134, 107, 255, 0.5)',
                  backgroundColor: 'rgba(134, 107, 255, 0.3)',
                  boxShadow: '0 0 15px rgba(134, 107, 255, 0.25), inset 0 0 15px rgba(134, 107, 255, 0.15)'
                } : {
                  backgroundColor: 'rgba(255, 255, 255, 0.025)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  borderRadius: '0.75rem',
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
                variants={item}
              >
                {plan.isPopular && (
                  <>
                    <div className="absolute inset-0 rounded-xl overflow-hidden opacity-20" style={{
                      background: 'linear-gradient(to bottom, rgba(134, 107, 255, 0.08) 0%, rgba(134, 107, 255, 0) 70%)',
                      pointerEvents: 'none'
                    }}></div>
                    <div className="absolute top-0 right-0 text-white text-xs px-3 py-1 font-body" style={{
                      border: '1px solid rgba(134, 107, 255, 0.25)',
                      zIndex: 10
                    }}>
                      {t('recommended')}
                    </div>
                  </>
                )}
                <div className="mb-4 relative z-10">
                  <h3 className="text-xl font-bold font-title">{t(plan.nameKey)}</h3>
                </div>

                <div className="mb-4 relative z-10">
                  <div className="flex items-baseline">
                    {plan.discounts[billingCycle] && plan.discounts[billingCycle] !== '0%' ? (
                      <>
                        <span className="text-5xl font-bold mr-2 font-title">
                          {language === 'kh' ? (
                            <>
                              {plan.pricesKh[billingCycle].toLocaleString()}៛
                            </>
                          ) : (
                            <>
                              ${plan.prices[billingCycle]}
                            </>
                          )}
                        </span>
                        <span className="line-through text-gray-500 mr-2">
                          {language === 'kh' ? (
                            <>
                              {(plan.basePrice * 4000).toLocaleString()}៛
                            </>
                          ) : (
                            <>
                              ${plan.basePrice}
                            </>
                          )}
                        </span>
                        <span className="text-xs bg-jade-purple/50 text-white px-2 py-1 rounded-full absolute top-0 right-0">
                          {plan.discounts[billingCycle]}
                        </span>
                      </>
                    ) : (
                      <>
                        <span className="text-5xl font-bold font-title">
                          {language === 'kh' ? (
                            <>
                              {plan.pricesKh[billingCycle].toLocaleString()}៛
                            </>
                          ) : (
                            <>
                              ${plan.prices[billingCycle]}
                            </>
                          )}
                        </span>
                      </>
                    )}
                    <span className="text-sm text-gray-400 ml-1"></span>
                  </div>
                </div>
                <div className="space-y-3 mb-6 flex-grow relative z-10">
                  <div className="flex items-center">
                    <span className="font-medium mr-2 font-body text-base sm:text-base">{t('messages_label')}</span>
                    <span className="font-body text-sm sm:text-base">
                      {language === 'kh' && plan.messagesKh ? plan.messagesKh : plan.messages}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium mr-2 font-body text-base sm:text-base">{t('channels_label')}</span>
                    <span className="font-body text-sm sm:text-base">
                      {language === 'kh' && plan.accountsKh ? plan.accountsKh : plan.accounts}
                    </span>
                  </div>
                </div>
                <button
                  className={`w-full py-2 rounded-md font-medium transition duration-300 mt-auto relative z-10 text-center block ${
                    plan.nameKey.replace('plan_', '').toLowerCase() === subscriptionData.plan_type?.toLowerCase()
                      ? 'btn-primary'
                      : index === 2
                        ? 'btn-secondary opacity-60 cursor-not-allowed'
                        : 'btn-secondary'
                  }`}
                  onClick={(e) => {
                    // Prevent default action for "Coming Soon" plan
                    if (index === 2) {
                      e.preventDefault();
                      return;
                    }

                    // Set the selected plan and show payment processor
                    const planType = plan.nameKey.replace('plan_', '').toLowerCase();
                    setSelectedPlan(planType);
                    setShowPaymentProcessor(true);
                  }}
                >
                  {index === 2
                    ? t('coming_soon_plan')
                    : (language === 'kh' 
                        ? t(plan.nameKey).toLowerCase() === subscriptionData.plan_type?.toLowerCase()
                        : plan.nameKey.replace('plan_', '').toLowerCase() === subscriptionData.plan_type?.toLowerCase())
                      ? t('pay')
                      : t('change_plan')}
                </button>
              </motion.div>
            ))}
          </motion.div>

          {/* Payment Processor Modal */}
          {showPaymentProcessor && selectedPlan && (
            <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <div className="w-full max-w-md">
                <PaymentProcessor
                  planType={selectedPlan}
                  billingCycle={billingCycle}
                  onCancel={() => {
                    setShowPaymentProcessor(false);
                    setSelectedPlan(null);
                  }}
                />
              </div>
            </div>
          )}
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
