'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'

export default function MessagesPage() {
  const [isLoading, setIsLoading] = useState(false)

  return (
    <div className="min-h-screen bg-black flex flex-col">
      <div className="flex-grow container mx-auto px-4 pt-20 pb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="text-sm text-zinc-400 hover:text-white">
                ← Back
              </Link>
              <h1 className="text-2xl md:text-3xl font-bold font-title">
                <span className="gradient-text">Message</span> History
              </h1>
            </div>
          </div>

          <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 mb-8">
            <h2 className="text-xl font-bold mb-4 font-title">Message Usage</h2>
            <div className="mb-4">
              <div className="h-4 bg-zinc-800 rounded-full overflow-hidden">
                <div className="h-full bg-jade-purple" style={{ width: '55%' }}></div>
              </div>
              <div className="flex justify-between mt-2">
                <span className="text-sm text-zinc-400 font-body">1,111 used</span>
                <span className="text-sm text-zinc-400 font-body">2,000 total</span>
              </div>
            </div>
            <p className="text-zinc-400 mb-6 font-body">
              You are currently using 55% of your available messages. Consider upgrading your plan if you expect to handle more messages this month.
            </p>
            <div className="flex justify-end">
              <button 
                className="btn-primary"
                onClick={() => setIsLoading(true)}
                disabled={isLoading}
              >
                {isLoading ? 'Processing...' : 'Add More Messages'}
              </button>
            </div>
          </div>

          <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
            <h2 className="text-xl font-bold mb-4 font-title">Message History</h2>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-zinc-800">
                    <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Date</th>
                    <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Channel</th>
                    <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Amount</th>
                    <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-zinc-800">
                    <td className="py-3 px-4 font-body">Apr 10, 2025</td>
                    <td className="py-3 px-4 font-body">Facebook</td>
                    <td className="py-3 px-4 font-body">500</td>
                    <td className="py-3 px-4 font-body"><span className="text-green-500">Delivered</span></td>
                  </tr>
                  <tr className="border-b border-zinc-800">
                    <td className="py-3 px-4 font-body">Apr 5, 2025</td>
                    <td className="py-3 px-4 font-body">Instagram</td>
                    <td className="py-3 px-4 font-body">300</td>
                    <td className="py-3 px-4 font-body"><span className="text-green-500">Delivered</span></td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 font-body">Apr 1, 2025</td>
                    <td className="py-3 px-4 font-body">All Channels</td>
                    <td className="py-3 px-4 font-body">1,200</td>
                    <td className="py-3 px-4 font-body"><span className="text-green-500">Delivered</span></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
} 