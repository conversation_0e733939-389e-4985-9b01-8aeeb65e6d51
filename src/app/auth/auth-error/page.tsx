'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Footer from '@/components/Footer'

export default function AuthError() {
  return (
    <div className="min-h-screen bg-black flex flex-col">
      <div className="px-4 py-8 mx-auto flex items-center justify-center">
        <Link href="/" className="text-2xl font-bold font-title">
          <span className="gradient-text">Chhlat</span><span className="text-white">Bot</span>
        </Link>
      </div>

      <div className="flex-grow flex items-center justify-center px-4 py-12">
        <motion.div 
          className="w-full max-w-md p-8 rounded-xl border border-zinc-800 shadow-xl text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="w-16 h-16 mx-auto mb-6 bg-red-900/20 rounded-full flex items-center justify-center">
            <svg 
              className="w-8 h-8 text-red-500" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </div>
          
          <h1 className="text-2xl sm:text-3xl font-bold mb-4 font-title">
            Authentication <span className="text-red-500">Error</span>
          </h1>
          
          <p className="mb-8 text-gray-300">
            There was a problem with your authentication request.
            The link may have expired or is invalid.
          </p>
          
          <div className="space-y-4">
            <Link 
              href="/login" 
              className="block w-full py-3 px-6 text-center bg-jade-purple text-white font-medium rounded-lg hover:bg-opacity-90 transition-all duration-200"
            >
              Return to Login
            </Link>
            
            <Link 
              href="/" 
              className="block w-full py-3 px-6 text-center border border-zinc-700 text-white font-medium rounded-lg hover:bg-zinc-900 transition-all duration-200"
            >
              Go to Homepage
            </Link>
          </div>
        </motion.div>
      </div>
      
      <Footer />
    </div>
  )
} 