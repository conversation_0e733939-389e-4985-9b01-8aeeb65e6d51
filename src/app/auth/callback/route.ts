import { NextResponse } from 'next/server'
import { type EmailOtpType } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/dashboard'

  // Always use the environment variable for consistency
  const redirectTo = process.env.NEXT_PUBLIC_SITE_URL
  
  const cookieStore = cookies()
  
  // Create the Supabase client
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll: () => {
          return cookieStore.getAll().map((cookie) => ({
            name: cookie.name,
            value: cookie.value,
          }))
        },
        setAll: (cookiesList) => {
          try {
            cookiesList.forEach(({ name, value, options }) => {
              cookieStore.set({ name, value, ...options })
            })
          } catch (error) {
            console.error('Failed to set cookies:', error)
          }
        },
      },
    }
  )

  try {
    if (code) {
      // If we have a code, we're handling an OAuth callback
      const { error } = await supabase.auth.exchangeCodeForSession(code)
      
      if (error) {
        console.error('Error exchanging code for session:', error)
        return NextResponse.redirect(`${redirectTo}/auth/auth-error`)
      }
      
      return NextResponse.redirect(`${redirectTo}${next}`)
    } else if (token_hash && type) {
      // Email auth or magic link auth
      const { error } = await supabase.auth.verifyOtp({
        type,
        token_hash,
      })

      if (error) {
        console.error('Error verifying OTP:', error)
        return NextResponse.redirect(`${redirectTo}/auth/auth-error`)
      }
      
      return NextResponse.redirect(`${redirectTo}${next}`)
    }
    
    // No valid auth parameters provided
    return NextResponse.redirect(`${redirectTo}/auth/auth-error`)
  } catch (error) {
    console.error('Unexpected error in auth callback:', error)
    return NextResponse.redirect(`${redirectTo}/auth/auth-error`)
  }
} 