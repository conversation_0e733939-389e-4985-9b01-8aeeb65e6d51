import { NextResponse } from 'next/server';
import { setRedisValue } from '@/utils/redis/client';

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();
    const { code, plan, username, lang } = body;

    // Validate required parameters (sector is no longer required)
    if (!code || !plan || !username || !lang) {
      return NextResponse.json(
        { error: 'Missing required parameters: code, plan, username, lang' },
        { status: 400 }
      );
    }

    // Store registration data in Redis with 15-minute expiration (without sector)
    const registrationData = { plan, username, lang };
    const success = await setRedisValue(`registration:${code}`, registrationData, 900); // 15 minutes expiration

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to store registration data' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in store-registration API:', error);
    return NextResponse.json(
      { error: 'Failed to store registration data' },
      { status: 500 }
    );
  }
}
