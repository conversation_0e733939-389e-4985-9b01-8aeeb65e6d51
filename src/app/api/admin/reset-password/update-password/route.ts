import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'
import { verifyAdminAuth } from '@/utils/auth'

export async function POST(request: Request) {
  try {
    // Verify admin authentication using optimized auth
    const { authenticated, isAdmin } = await verifyAdminAuth()
    if (!authenticated || !isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      )
    }

    // Now that we've verified admin status, use the service role client
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Get the user ID and password from the request
    const { userId, password } = await request.json()
    if (!userId || !password) {
      return NextResponse.json(
        { error: 'User ID and password are required' },
        { status: 400 }
      )
    }

    try {
      // Use client ID directly to update auth password
      console.log('Updating auth user password for client ID:', userId)
      const { error: updateError } = await adminClient.auth.admin.updateUserById(
        userId,
        { password: password }
      )

      if (updateError) {
        console.error('Error updating auth user password:', updateError)
        return NextResponse.json(
          { error: 'Failed to update password: ' + updateError.message },
          { status: 500 }
        )
      }

      console.log('Successfully updated auth user password')

      // Success - return a success response
      return NextResponse.json({
        success: true,
        message: 'Password updated successfully'
      })
    } catch (error) {
      console.error('Error updating password:', error)
      return NextResponse.json(
        { error: 'Failed to update password: ' + (error instanceof Error ? error.message : 'Unknown error') },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Server error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}