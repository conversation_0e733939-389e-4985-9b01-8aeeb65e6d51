import { NextResponse } from 'next/server'
import { getDashboardData } from '@/utils/dashboard'
import { verifyAuth } from '@/utils/auth'

export async function GET() {
  try {
    // Verify authentication using optimized RLS-based auth
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get dashboard data directly from database (client-side caching handles performance)
    const dashboardData = await getDashboardData()

    if (!dashboardData) {
      return NextResponse.json({
        error: 'Failed to load dashboard data'
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: dashboardData
    })
  } catch (error) {
    console.error('Error in dashboard data API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
