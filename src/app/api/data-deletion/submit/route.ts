import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json()
    const { email } = body

    // Validate email
    if (!email || !email.trim()) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Get webhook URL from environment variables
    const webhookUrl = process.env.DATA_DELETION_WEBHOOK_URL

    if (!webhookUrl) {
      console.error('DATA_DELETION_WEBHOOK_URL not configured')
      return NextResponse.json(
        { error: 'Service configuration error' },
        { status: 500 }
      )
    }

    // Send email to webhook
    const webhookResponse = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: email.trim(),
        timestamp: new Date().toISOString(),
        source: 'data-deletion-form'
      }),
    })

    if (!webhookResponse.ok) {
      console.error('Webhook request failed:', webhookResponse.status, webhookResponse.statusText)
      return NextResponse.json(
        { error: 'Failed to process deletion request' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true,
      message: 'Data deletion request submitted successfully'
    })

  } catch (error) {
    console.error('Error in data deletion API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
