import { NextResponse } from 'next/server';

/**
 * Send a webhook notification for welcome/intro message updates
 * @param welcomeData Object containing welcome message information
 * @returns Promise that resolves when the webhook is sent (fire and forget)
 */
export async function sendWelcomeUpdateWebhook(welcomeData: {
  chat_id: string;
  client_id: string;
  answer: string;
  lang?: string;
  sector?: string | null;
  audio_url?: string;
  is_photo?: boolean;
}) {
  try {
    // Call our internal API route instead of the external webhook directly
    fetch('/api/webhooks/welcome/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(welcomeData),
    }).catch(error => {
      // Log any errors but don't throw - this is fire and forget
      console.error('Error sending welcome update webhook:', error);
    });

  } catch (error) {
    console.error('Error initiating welcome update webhook:', error);
  }
}
