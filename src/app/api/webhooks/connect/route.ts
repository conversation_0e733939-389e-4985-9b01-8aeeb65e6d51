import { NextResponse } from 'next/server';
import { verifyAuth } from '@/utils/auth';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, clientId } = await verifyAuth();
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the connection webhook URL from environment variables
    const connectionWebhookUrl = process.env.CONNECTION_WEBHOOK_URL;

    if (!connectionWebhookUrl) {
      console.error('CONNECTION_WEBHOOK_URL environment variable not configured');
      return NextResponse.json({ error: 'Connection service endpoint not configured.' }, { status: 500 });
    }

    // Parse the request body
    const payload = await request.json();
    const { client_id, webhook_url, token, type } = payload;

    // Validate required fields
    // Note: token can be empty for Telegram Business connections
    const isTokenRequired = type !== 'telegram_biz';
    if (!client_id || (isTokenRequired && !token) || !type) {
      console.error('API route received incomplete data:', { client_id: client_id ? 'Present' : 'Missing', token: token ? 'Present' : 'Missing', type: type ? 'Present' : 'Missing' });
      return NextResponse.json({ error: 'Missing required fields for platform connection.' }, { status: 400 });
    }

    // Verify that the client_id matches the authenticated user
    if (client_id !== clientId) {
      return NextResponse.json({ error: 'Client ID mismatch' }, { status: 403 });
    }



    // Check connection limits before proceeding
    try {
      // Create Supabase client
      const cookieStore = cookies();
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get(name: string) {
              return cookieStore.get(name)?.value;
            },
            set() {
              // Not needed for this context
            },
            remove() {
              // Not needed for this context
            },
          },
        }
      );

      // Get client's plan type
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('plan_type')
        .eq('client_id', client_id)
        .single();

      if (clientError || !clientData) {
        console.error('Error fetching client data:', clientError);
        return NextResponse.json(
          { error: 'Failed to verify account information' },
          { status: 500 }
        );
      }

      // Get plan connection limit
      const { data: planData, error: planError } = await supabase
        .from('plans')
        .select('connections')
        .eq('name', clientData.plan_type)
        .single();

      if (planError || !planData) {
        console.error('Error fetching plan data:', planError);
        return NextResponse.json(
          { error: 'Failed to verify plan limits' },
          { status: 500 }
        );
      }

      const connectionLimit = planData.connections || 1;

      // Get current connections count
      const { data: credentialsData, error: credentialsError } = await supabase
        .from('client_credentials')
        .select('fb_id, ig_id, tg_id')
        .eq('client_id', client_id)
        .single();

      if (credentialsError || !credentialsData) {
        console.error('Error fetching credentials data:', credentialsError);
        return NextResponse.json(
          { error: 'Failed to verify current connections' },
          { status: 500 }
        );
      }

      // Count current connections (non-empty IDs)
      let currentConnections = 0;
      if (credentialsData.fb_id) currentConnections++;
      if (credentialsData.ig_id) currentConnections++;
      if (credentialsData.tg_id) currentConnections++;

      // Determine which platform is being connected
      let platformType = type;
      if (type.includes('/')) {
        platformType = type.split('/')[0]; // Handle cases like "instagram/page_id"
      }
      if (type === 'telegram_biz') {
        platformType = 'telegram'; // Both telegram and telegram_biz use tg_id
      }

      // Check if this platform is already connected
      const platformConnected = {
        facebook: !!credentialsData.fb_id,
        instagram: !!credentialsData.ig_id,
        telegram: !!credentialsData.tg_id
      };

      // Only validate limit if this is a new connection
      if (!platformConnected[platformType as keyof typeof platformConnected] && currentConnections >= connectionLimit) {
        console.log(`Connection limit reached for client ${client_id}: ${currentConnections}/${connectionLimit}`);
        return NextResponse.json(
          { error: `Connection limit reached (${currentConnections}/${connectionLimit}). Please upgrade your plan to connect more platforms.` },
          { status: 403 }
        );
      }

      console.log(`Connection limit check passed for client ${client_id}: ${currentConnections}/${connectionLimit}`);
    } catch (limitError) {
      console.error('Error checking connection limits:', limitError);
      return NextResponse.json(
        { error: 'Failed to verify connection limits' },
        { status: 500 }
      );
    }

    console.log(`Forwarding connection request to webhook for client: ${client_id}, platform: ${type}`);

    // Forward the request to the actual webhook
    const response = await fetch(connectionWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id,
        webhook_url,
        token,
        type
      }),
    });

    // Check if webhook reported success (status 2xx)
    if (!response.ok) {
      const errorBody = await response.text(); // Get error details if available
      console.error(`Connection webhook failed with status ${response.status}:`, errorBody);

      // Return the error to the client with the appropriate status code
      return NextResponse.json(
        { error: `Platform connection service failed (Status: ${response.status})` },
        { status: response.status }
      );
    }

    console.log(`Successfully connected platform ${type} for client: ${client_id}`);

    // Return success to the client
    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error in connection webhook API route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process connection request.' },
      { status: 500 }
    );
  }
}
