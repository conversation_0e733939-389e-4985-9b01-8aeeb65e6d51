import { NextResponse } from 'next/server';
export async function POST(request: Request) {
  try {
    const welcomeData = await request.json();

    // Always send to WELCOME_EN_WEBHOOK_URL only
    const webhookUrl = process.env.WELCOME_EN_WEBHOOK_URL;

    if (!webhookUrl) {
      console.warn('WELCOME_EN_WEBHOOK_URL not configured');
      return NextResponse.json({ error: 'Welcome webhook URL not configured' }, { status: 500 });
    }

    // Forward the request to the actual webhook
    await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(welcomeData),
    });

    // Return success
    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error sending welcome update webhook:', error);
    return NextResponse.json({ error: 'Failed to send welcome webhook' }, { status: 500 });
  }
}
