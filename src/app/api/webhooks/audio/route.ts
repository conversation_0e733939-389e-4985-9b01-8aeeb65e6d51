import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const audioWebhookUrl = process.env.AUDIO_WEBHOOK_URL;
    if (!audioWebhookUrl) {
      console.error('AUDIO_WEBHOOK_URL environment variable not configured');
      return NextResponse.json({ error: 'Audio processing service endpoint not configured.' }, { status: 500 });
    }

    // Get payload including Base64 audio and target path
    const payload = await request.json();
    const {
      userId,
      bucket,
      filePath,
      audioData, // Expecting Base64 string
      mimeType   // Mime type of the original audio blob
    } = payload;

    // Validate required fields for n8n
    if (!userId || !filePath || !audioData || !mimeType || !bucket) {
      console.error('API route received incomplete data:', { userId, bucket, filePath, audioData: audioData ? 'Present' : 'Missing', mimeType });
      return NextResponse.json({ error: 'Missing required fields for audio processing.' }, { status: 400 });
    }

    console.log(`Forwarding audio upload task to n8n for user: ${userId}, path: ${filePath}`);

    // Forward only necessary data to the n8n webhook
    const n8nResponse = await fetch(audioWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add any auth headers for n8n if needed here
      },
      body: JSON.stringify({
        userId,
        bucket,
        filePath,
        audioData, // Base64 string
        mimeType
      })
    });

    // Check if n8n reported success (status 2xx)
    if (!n8nResponse.ok) {
      const errorBody = await n8nResponse.text(); // Get error details from n8n if available
      console.error(`n8n webhook upload failed with status ${n8nResponse.status}:`, errorBody);
      // Return a generic error to the client
      throw new Error(`Audio upload service failed (Status: ${n8nResponse.status})`);
    }

    console.log(`n8n successfully processed upload for path: ${filePath}`);

    // Return simple success to the client, signaling n8n finished the upload
    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error in audio webhook API route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process audio upload request.' },
      { status: 500 }
    );
  }
}