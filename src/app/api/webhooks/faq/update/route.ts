import { NextResponse } from 'next/server';
export async function POST(request: Request) {
  try {
    const faqData = await request.json();

    // Always send to FAQ_UPDATE_EN_WEBHOOK_URL only
    const webhookUrl = process.env.FAQ_UPDATE_EN_WEBHOOK_URL;

    if (!webhookUrl) {
      console.warn('FAQ_UPDATE_EN_WEBHOOK_URL not configured');
      return NextResponse.json({ error: 'Update webhook URL not configured' }, { status: 500 });
    }

    // Forward the request to the actual webhook
    await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(faqData),
    });

    // Return success
    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error sending FAQ update webhook:', error);
    return NextResponse.json({ error: 'Failed to send update webhook' }, { status: 500 });
  }
}
