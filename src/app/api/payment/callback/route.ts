import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Helper function to log request details
async function logRequestDetails(request: Request, method: string) {
  console.log(`Payment callback ${method} received - URL:`, request.url);

  try {
    // Log headers
    console.log('Payment callback - Headers:');
    request.headers.forEach((value, key) => {
      console.log(`${key}: ${value}`);
    });

    // For GET requests, log query parameters
    if (method === 'GET') {
      const url = new URL(request.url);
      console.log('Payment callback - Query parameters:');
      url.searchParams.forEach((value, key) => {
        console.log(`${key}: ${value}`);
      });
    }
  } catch (error) {
    console.error('Error logging request details:', error);
  }
}

export async function GET(request: Request) {
  try {
    // Log request details
    await logRequestDetails(request, 'GET');

    // Extract query parameters
    const url = new URL(request.url);
    const tran_id = url.searchParams.get('tran_id');
    const status = url.searchParams.get('status');
    const status_code = url.searchParams.get('status_code');
    const status_message = url.searchParams.get('status_message');
    const approval_code = url.searchParams.get('approval_code');
    const custom_fields = url.searchParams.get('custom_fields');

    // Log the payment response
    console.log('Payment callback GET - Extracted data:', {
      tran_id,
      status,
      status_code,
      status_message,
      approval_code,
      custom_fields
    });

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Payment callback received via GET'
    });

  } catch (error: any) {
    console.error('Error in payment callback GET API:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process payment callback' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Log request details
    await logRequestDetails(request, 'POST');

    // Log the raw request for debugging
    console.log('Payment callback POST received - Raw request:', request);

    // Parse the request body
    const formData = await request.formData();

    // Log all form data entries for debugging
    console.log('Payment callback - All form data:');
    for (const [key, value] of formData.entries()) {
      console.log(`${key}: ${value}`);
    }

    // Extract payment response data
    const tran_id = formData.get('tran_id') as string;
    const status = formData.get('status') as string;
    const status_code = formData.get('status_code') as string;
    const status_message = formData.get('status_message') as string;
    const approval_code = formData.get('approval_code') as string;
    const custom_fields = formData.get('custom_fields') as string;

    // Log the payment response
    console.log('Payment callback - Extracted data:', {
      tran_id,
      status,
      status_code,
      status_message,
      approval_code,
      custom_fields
    });

    // Validate required parameters
    if (!tran_id || !status || !status_code || !custom_fields) {
      console.error('Missing required parameters in payment callback');
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Decode custom fields (base64 encoded JSON)
    let decodedCustomFields;
    try {
      const decodedString = Buffer.from(custom_fields, 'base64').toString('utf-8');
      decodedCustomFields = JSON.parse(decodedString);
    } catch (error) {
      console.error('Error decoding custom fields:', error);
      return NextResponse.json(
        { error: 'Invalid custom fields format' },
        { status: 400 }
      );
    }

    // Extract client information from custom fields
    const { client_id, plan_type, billing_cycle } = decodedCustomFields;

    // Validate client information
    if (!client_id || !plan_type || billing_cycle === undefined) {
      console.error('Missing client information in custom fields');
      return NextResponse.json(
        { error: 'Missing client information' },
        { status: 400 }
      );
    }

    // Check if payment was successful
    if (status_code !== '00') {
      console.error('Payment failed:', status_message);
      return NextResponse.json(
        { error: 'Payment failed', status_message },
        { status: 400 }
      );
    }

    // Create Supabase admin client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Calculate next billing date based on billing cycle
    const now = new Date();
    let nextBillingDate = new Date();

    if (billing_cycle === 0) {
      // 1-month plan
      nextBillingDate.setMonth(now.getMonth() + 1);
    } else if (billing_cycle === 1) {
      // 3-month plan
      nextBillingDate.setMonth(now.getMonth() + 3);
    } else {
      // Default to 1 month
      nextBillingDate.setMonth(now.getMonth() + 1);
    }

    // Format next billing date as ISO string
    const next_billing_date = nextBillingDate.toISOString().split('T')[0];

    // Update client subscription in database
    const { data, error } = await supabase
      .from('clients')
      .update({
        plan_type,
        next_billing_date,
        subscription_status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('client_id', client_id);

    if (error) {
      console.error('Error updating client subscription:', error);
      return NextResponse.json(
        { error: 'Failed to update subscription' },
        { status: 500 }
      );
    }

    // Log payment transaction
    const { data: transactionData, error: transactionError } = await supabase
      .from('payment_transactions')
      .insert({
        client_id,
        transaction_id: tran_id,
        amount: formData.get('amount'),
        currency: formData.get('currency'),
        status: status_code,
        status_message,
        approval_code,
        payment_method: formData.get('payment_option'),
        plan_type,
        billing_cycle
      });

    if (transactionError) {
      console.error('Error logging payment transaction:', transactionError);
      // Continue despite transaction logging error
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Payment processed successfully'
    });

  } catch (error: any) {
    console.error('Error in payment callback API:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process payment callback' },
      { status: 500 }
    );
  }
}
