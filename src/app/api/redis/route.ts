import { NextResponse } from 'next/server';
import { verifyAuth } from '@/utils/auth';
import {
  getRedisValue,
  setRedisValue,
  deleteRedisValue,
  hasRedisKey,
  getHashField,
  setHashField,
  getHashAll
} from '@/utils/redis/client';

export async function GET(request: Request) {
  try {
    // Verify authentication using optimized RLS-based auth
    const { authenticated } = await verifyAuth();
    if (!authenticated) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get parameters from URL
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');
    const hash = searchParams.get('hash');
    const field = searchParams.get('field');

    // Validate required parameters
    if (!key && (!hash || !field)) {
      return NextResponse.json(
        { error: 'Missing required parameters. Either "key" or both "hash" and "field" must be provided.' },
        { status: 400 }
      );
    }

    let result;

    // Handle hash operations
    if (hash) {
      if (field) {
        // Get specific hash field
        result = await getHashField(hash, field);
      } else {
        // Get all hash fields
        result = await getHashAll(hash);
      }
    } else if (key) {
      // Get simple key
      result = await getRedisValue(key);
    }

    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('Error in Redis GET API:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve data from Redis' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Verify authentication using optimized RLS-based auth
    const { authenticated } = await verifyAuth();
    if (!authenticated) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { key, value, expireInSeconds, hash, field } = body;

    // Validate required parameters
    if (!key && (!hash || !field)) {
      return NextResponse.json(
        { error: 'Missing required parameters. Either "key" or both "hash" and "field" must be provided.' },
        { status: 400 }
      );
    }

    if ((key && value === undefined) || (hash && field && value === undefined)) {
      return NextResponse.json(
        { error: 'Value must be provided' },
        { status: 400 }
      );
    }

    let success;

    // Handle hash operations
    if (hash && field) {
      success = await setHashField(hash, field, value);
    } else {
      // Set simple key
      success = await setRedisValue(key, value, expireInSeconds);
    }

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to set value in Redis' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in Redis POST API:', error);
    return NextResponse.json(
      { error: 'Failed to set data in Redis' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    // Verify authentication
    const { authenticated, userId } = await verifyAuth();
    if (!authenticated) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get parameters from URL
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    // Validate required parameters
    if (!key) {
      return NextResponse.json(
        { error: 'Missing required parameter: key' },
        { status: 400 }
      );
    }

    const success = await deleteRedisValue(key);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete value from Redis' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in Redis DELETE API:', error);
    return NextResponse.json(
      { error: 'Failed to delete data from Redis' },
      { status: 500 }
    );
  }
}
