import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { verifyAuth } from '@/utils/auth'
import { getRedisValue, setRedisValue } from '@/utils/redis/client'

export async function GET() {
  try {
    // Verify authentication using optimized RLS-based auth
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Try to get from cache first (30 minutes TTL)
    const cacheKey = `knowledge:stats:${clientId}`
    const cached = await getRedisValue<any>(cacheKey)
    
    if (cached) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true
      })
    }

    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get client info with plan type - RLS automatically filters by client_id
    const { data: clientData, error: clientError } = await supabase
      .from('clients')
      .select('client_id, plan_type')
      .single()

    if (clientError || !clientData) {
      console.error('Error fetching client data:', clientError)
      return NextResponse.json({ 
        error: 'Failed to fetch client data' 
      }, { status: 500 })
    }

    // Get FAQ count - RLS automatically filters by client_id
    const { count: faqCount, error: faqError } = await supabase
      .from('faqs')
      .select('client_id', { count: 'exact', head: true })

    if (faqError) {
      console.error('Error fetching FAQ count:', faqError)
      return NextResponse.json({ 
        error: 'Failed to fetch FAQ count' 
      }, { status: 500 })
    }

    // Get photo count - RLS automatically filters by client_id
    const { count: photoCount, error: photoError } = await supabase
      .from('photos')
      .select('client_id', { count: 'exact', head: true })

    if (photoError) {
      console.error('Error fetching photo count:', photoError)
      return NextResponse.json({ 
        error: 'Failed to fetch photo count' 
      }, { status: 500 })
    }

    // Get plan limits from plans table
    const { data: planData, error: planError } = await supabase
      .from('plans')
      .select('name, total_faqs, total_photos')

    if (planError) {
      console.error('Error fetching plan data:', planError)
      return NextResponse.json({ 
        error: 'Failed to fetch plan data' 
      }, { status: 500 })
    }

    // Find matching plan (case-insensitive)
    const matchingPlan = planData?.find(plan =>
      plan.name.trim().toLowerCase() === clientData.plan_type?.trim().toLowerCase()
    )

    // Calculate usage percentages
    const faqLimit = matchingPlan?.total_faqs || 0
    const photoLimit = matchingPlan?.total_photos || 0
    
    const faqUsagePercentage = faqLimit > 0 
      ? Math.min((faqCount || 0) / faqLimit * 100, 100) 
      : 0
    
    const photoUsagePercentage = photoLimit > 0 
      ? Math.min((photoCount || 0) / photoLimit * 100, 100) 
      : 0

    const knowledgeStats = {
      counts: {
        faqs: faqCount || 0,
        photos: photoCount || 0
      },
      limits: {
        faqs: faqLimit,
        photos: photoLimit,
        plan_name: matchingPlan?.name || clientData.plan_type || 'Unknown'
      },
      usage: {
        faq_percentage: Math.round(faqUsagePercentage),
        photo_percentage: Math.round(photoUsagePercentage)
      }
    }

    // Cache the result for 30 minutes (1800 seconds)
    await setRedisValue(cacheKey, knowledgeStats, 1800)

    return NextResponse.json({
      success: true,
      data: knowledgeStats,
      cached: false
    })
  } catch (error) {
    console.error('Error in knowledge stats API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
