import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST(request: Request) {
  try {
    // Create Supabase client for server-side
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: any) {
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          },
        },
      }
    )

    // Sign out from Supabase
    await supabase.auth.signOut()

    // Create response with cleared auth cookies
    const response = NextResponse.json({ success: true })

    // Clear auth-related cookies by setting them to expire
    const authCookieNames = [
      'sb-access-token',
      'sb-refresh-token',
      'sb-auth-token',
      'sb-provider-token',
      'sb-provider-refresh-token'
    ]

    // Clear known Supabase cookies
    authCookieNames.forEach(cookieName => {
      response.cookies.set({
        name: cookieName,
        value: '',
        maxAge: 0,
        path: '/',
      })
    })

    // Clear any existing cookies from the cookie store
    const allCookies = cookieStore.getAll()
    allCookies.forEach(cookie => {
      if (
        cookie.name.includes('sb-') ||
        cookie.name.includes('-auth-token') ||
        cookie.name.includes('supabase') ||
        cookie.name.includes('google_logout')
      ) {
        response.cookies.set({
          name: cookie.name,
          value: '',
          maxAge: 0,
          path: '/',
        })
      }
    })

    return response
  } catch (error) {
    console.error('Error in signout endpoint:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to sign out' },
      { status: 500 }
    )
  }
}