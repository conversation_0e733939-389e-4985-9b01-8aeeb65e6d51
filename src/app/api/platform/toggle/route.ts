import { NextResponse } from 'next/server';
import { verifyAuth } from '@/utils/auth';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { setRedisValue } from '@/utils/redis/client';

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, clientId } = await verifyAuth();
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { platform, status } = body;

    // Validate required parameters
    if (!platform || typeof status !== 'boolean') {
      return NextResponse.json(
        { error: 'Missing required parameters: platform (string), status (boolean)' },
        { status: 400 }
      );
    }

    // Validate platform
    const validPlatforms = ['facebook', 'instagram', 'telegram', 'web'];
    if (!validPlatforms.includes(platform)) {
      return NextResponse.json(
        { error: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}` },
        { status: 400 }
      );
    }

    // Create Supabase client
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    );

    // Get current credentials (needed for both validation and Redis update)
    const { data: credentialsData, error: credentialsError } = await supabase
      .from('client_credentials')
      .select('fb_id, ig_id, tg_id, fb_url, ig_url, tg_url, tg_biz_id')
      .eq('client_id', clientId)
      .single();

    if (credentialsError || !credentialsData) {
      console.error('Error fetching credentials data:', credentialsError);
      return NextResponse.json(
        { error: 'Failed to verify current connections' },
        { status: 500 }
      );
    }

    // If trying to enable, perform validation checks
    if (status === true) {
      // 1. Check billing status
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('plan_type, next_billing_date')
        .eq('client_id', clientId)
        .single();

      if (clientError || !clientData) {
        console.error('Error fetching client data:', clientError);
        return NextResponse.json(
          { error: 'Failed to verify account status' },
          { status: 500 }
        );
      }

      // Check if billing is overdue
      if (clientData.next_billing_date) {
        const billingDate = new Date(clientData.next_billing_date);
        const today = new Date();
        const todayInPhnomPenh = new Date(today.toLocaleString('en-US', { timeZone: 'Asia/Phnom_Penh' }));
        todayInPhnomPenh.setHours(0, 0, 0, 0);

        if (billingDate < todayInPhnomPenh) {
          return NextResponse.json(
            { error: 'Cannot enable platform: Your plan has expired. Please renew your subscription.' },
            { status: 403 }
          );
        }
      }

      // 2. Check usage limits
      const { data: configData, error: configError } = await supabase
        .from('configs')
        .select('usage_used, usage_limit')
        .eq('client_id', clientId)
        .single();

      if (configError || !configData) {
        console.error('Error fetching config data:', configError);
        return NextResponse.json(
          { error: 'Failed to verify usage limits' },
          { status: 500 }
        );
      }

      const usageUsed = configData.usage_used || 0;
      const usageLimit = configData.usage_limit || 2000;

      if (usageUsed >= usageLimit) {
        return NextResponse.json(
          { error: 'Cannot enable platform: Usage limit reached. Please upgrade your plan.' },
          { status: 403 }
        );
      }

      // 3. Check connection limits
      const { data: planData, error: planError } = await supabase
        .from('plans')
        .select('connections')
        .eq('name', clientData.plan_type)
        .single();

      if (planError || !planData) {
        console.error('Error fetching plan data:', planError);
        return NextResponse.json(
          { error: 'Failed to verify connection limits' },
          { status: 500 }
        );
      }

      const connectionLimit = planData.connections || 1;

      // Count current connections (non-empty IDs)
      let currentConnections = 0;
      if (credentialsData.fb_id) currentConnections++;
      if (credentialsData.ig_id) currentConnections++;
      if (credentialsData.tg_id) currentConnections++;

      // Check if enabling this platform would exceed the limit
      // Only check if this platform is not already connected
      const platformConnected = {
        facebook: !!credentialsData.fb_id,
        instagram: !!credentialsData.ig_id,
        telegram: !!credentialsData.tg_id
      };

      if (!platformConnected[platform as keyof typeof platformConnected] && currentConnections >= connectionLimit) {
        return NextResponse.json(
          { error: `Cannot enable platform: Connection limit reached (${currentConnections}/${connectionLimit}). Please upgrade your plan.` },
          { status: 403 }
        );
      }
    }

    // Determine status field based on platform
    let statusField = '';
    let platformPrefix = '';
    
    switch (platform) {
      case 'facebook':
        statusField = 'fb_status';
        platformPrefix = 'fb';
        break;
      case 'instagram':
        statusField = 'ig_status';
        platformPrefix = 'ig';
        break;
      case 'telegram':
        statusField = 'tg_status';
        platformPrefix = 'tg';
        break;
      case 'web':
        statusField = 'web_status';
        platformPrefix = 'web';
        break;
      default:
        return NextResponse.json(
          { error: `Unknown platform: ${platform}` },
          { status: 400 }
        );
    }

    // Update database status
    const { error: updateError } = await supabase
      .from('client_credentials')
      .update({ [statusField]: status ? 1 : 0 })
      .eq('client_id', clientId);

    if (updateError) {
      console.error(`Error updating ${platform} status:`, updateError);
      return NextResponse.json(
        { error: `Failed to update ${platform} status in database` },
        { status: 500 }
      );
    }

    // Update Redis status using already fetched credentials
    try {
      if (credentialsData) {
        let redisIdentifier = '';

        if (platform === 'facebook') {
          const platformUrl = credentialsData.fb_url || '';
          if (platformUrl) {
            const urlParts = platformUrl.split('/');
            redisIdentifier = urlParts[urlParts.length - 1];
          }
        } else if (platform === 'instagram') {
          const platformUrl = credentialsData.ig_url || '';
          if (platformUrl) {
            const urlParts = platformUrl.split('/');
            redisIdentifier = urlParts[urlParts.length - 1];
          }
        } else if (platform === 'telegram') {
          if (credentialsData.tg_biz_id) {
            redisIdentifier = credentialsData.tg_biz_id;
          } else {
            const platformUrl = credentialsData.tg_url || '';
            if (platformUrl) {
              const urlParts = platformUrl.split('/');
              redisIdentifier = urlParts[urlParts.length - 1];
            }
          }
        }

        if (redisIdentifier) {
          const redisKey = `status:${platformPrefix}:${redisIdentifier}`;
          const redisValue = {
            status: status ? 1 : 0,
            clientId: clientId
          };

          const redisSuccess = await setRedisValue(redisKey, redisValue);
          if (!redisSuccess) {
            console.warn(`Warning: Failed to update Redis status for ${platform}`);
          }
        } else {
          console.warn(`Warning: No identifier found for ${platform}, skipping Redis update`);
        }
      }
    } catch (redisError) {
      console.warn(`Warning: Error updating Redis status for ${platform}:`, redisError);
      // Continue execution even if Redis update fails
    }

    return NextResponse.json({
      success: true,
      message: `${platform} status updated successfully`,
      status: status
    });

  } catch (error) {
    console.error('Error in platform toggle API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
