'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { useState } from 'react'

export default function DataDeletion() {
  const [email, setEmail] = useState('')
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email.trim()) {
      setError('Email is required')
      return
    }

    try {
      // Send email to data deletion webhook
      const response = await fetch('/api/data-deletion/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim()
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to submit deletion request')
      }

      setSubmitted(true)
      setError('')
    } catch (err) {
      console.error('Error submitting deletion request:', err)
      setError('An error occurred. Please try again.')
    }
  }

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Logo Header */}
      <div className="px-4 py-8 mx-auto flex items-center justify-center">
        <Link href="/">
          <img
            src="/images/white_tran_logo.svg"
            alt="ChhlatBot"
            className="h-10 w-auto"
          />
        </Link>
      </div>

      <motion.div
        className="flex-grow container mx-auto px-4 py-12 text-gray-300"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-3xl mx-auto p-8 rounded-xl border border-white/20 shadow-xl"
          style={{
            boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.30)'
          }}>
          <h1 className="text-3xl font-bold mb-8 text-white">Data Deletion Request</h1>

          <div className="prose prose-invert prose-zinc max-w-none">
            {!submitted ? (
              <>
                <p className="mb-6">You have the right to request deletion of your personal data collected and processed by ChhlatBot. This page explains how to submit a data deletion request and what data will be affected.</p>

                <h2 className="text-xl font-semibold mt-8 mb-4 text-white">What Data We Collect</h2>

                <h3 className="text-lg font-semibold mt-6 mb-3 text-white">Data We Can Delete Upon Request</h3>
                <p className="mb-4">ChhlatBot collects and processes the following personal data that can be deleted upon request:</p>
                <ul className="list-disc pl-6 mt-2 mb-4 space-y-2">
                  <li><strong>Account Information:</strong> Email address, username, business sector, language preferences</li>
                  <li><strong>Business Knowledge Base:</strong> Questions, replies, audio recordings, images you upload</li>
                  <li><strong>Social Media Data:</strong> Messages from connected Facebook, Instagram, Telegram accounts</li>
                  <li><strong>Conversation Data:</strong> Customer interactions processed through our AI system</li>
                  <li><strong>Usage Analytics:</strong> Service usage statistics, response performance metrics</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6 mb-3 text-white">Anonymized Analytics Data (Cannot Be Deleted)</h3>
                <p className="mb-4">We use privacy-focused web analytics for website improvement, which collects anonymized data that cannot be linked to individual users:</p>
                <ul className="list-disc pl-6 mt-2 mb-4 space-y-2">
                  <li><strong>Website Usage:</strong> Anonymized page views, session duration, referrer information</li>
                  <li><strong>Technical Information:</strong> Aggregated browser type, device type, screen resolution, operating system data</li>
                  <li><strong>Geographic Data:</strong> Country and region statistics (no IP addresses stored)</li>
                  <li><strong>Performance Data:</strong> Website speed and performance metrics</li>
                </ul>
                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-6">
                  <p className="text-blue-200 text-sm">
                    <strong>Important:</strong> This analytics data is completely anonymized, aggregated, and cannot be connected to any individual user. Therefore, it cannot be deleted on a per-user basis as it doesn't contain personal identifiers. This data helps us improve website performance and user experience while maintaining your privacy.
                  </p>
                </div>

                <h2 className="text-xl font-semibold mt-8 mb-4 text-white">How to Request Data Deletion</h2>

                <p className="mb-4">You can request deletion of your data in the following ways:</p>

                <h3 className="text-lg font-semibold mt-6 mb-3 text-white">Option 1: Email Request</h3>
                <p>Send an email to <a href="mailto:<EMAIL>" className="text-jade-purple hover:underline"><EMAIL></a> with the subject line "Data Deletion Request".</p>
                <p>Please include the following information:</p>
                <ul className="list-disc pl-6 mt-2 mb-4 space-y-2">
                  <li>The email address associated with your ChhlatBot account</li>
                  <li>Your full name</li>
                  <li>A statement requesting deletion of your data</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6 mb-3 text-white">Option 2: Contact Support for Account Deletion</h3>
                <p>Since ChhlatBot is designed for business owners, account deletion requires manual processing to ensure proper data handling. To delete your account:</p>
                <ol className="list-decimal pl-6 mt-2 mb-4 space-y-2">
                  <li>Contact our support team at <a href="mailto:<EMAIL>" className="text-jade-purple hover:underline"><EMAIL></a></li>
                  <li>Include your account email and request for complete account deletion</li>
                  <li>Our team will verify your identity and process the deletion</li>
                  <li>You will receive confirmation once the deletion is complete</li>
                </ol>

                <h3 className="text-lg font-semibold mt-6 mb-3 text-white">Option 3: Disconnect Social Media Platforms</h3>
                <p>To stop data collection from your social media accounts, you can disconnect them from ChhlatBot:</p>

                <h4 className="text-md font-semibold mt-4 mb-2 text-white">Facebook/Instagram:</h4>
                <ol className="list-decimal pl-6 mt-2 mb-4 space-y-1">
                  <li>Log into your ChhlatBot dashboard</li>
                  <li>Go to social media connections and delete the Facebook/Instagram connection</li>
                  <li>Visit <a href="https://developers.facebook.com/" target="_blank" className="text-jade-purple hover:underline">Facebook Developer Console</a></li>
                  <li>Find your app and disconnect any webhooks related to ChhlatBot</li>
                </ol>

                <h4 className="text-md font-semibold mt-4 mb-2 text-white">Telegram:</h4>
                <ol className="list-decimal pl-6 mt-2 mb-4 space-y-1">
                  <li>Log into your ChhlatBot dashboard</li>
                  <li>Go to social media connections and delete the Telegram connection</li>
                  <li>This will automatically stop Telegram from sending any information to our site</li>
                </ol>

                <p className="mb-4"><strong>Note:</strong> Disconnecting platforms will stop future data collection but won't delete data already collected. Use the form below for complete data deletion:</p>

                <form onSubmit={handleSubmit} className="mt-8 space-y-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-2">
                      Your Email Address*
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="w-full px-4 py-3 bg-zinc-800 border border-zinc-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple"
                      placeholder="Enter your email address"
                    />
                  </div>

                  {error && (
                    <div className="p-3 bg-red-900/30 border border-red-800 rounded-lg text-red-200 text-sm">
                      {error}
                    </div>
                  )}

                  <button
                    type="submit"
                    className="w-full py-3 px-6 text-center bg-jade-purple text-white font-medium rounded-lg hover:bg-opacity-90 transition-all duration-200"
                  >
                    Submit Deletion Request
                  </button>
                </form>
              </>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold mb-4 text-white">Request Received</h2>
                <p className="mb-6">We've received your data deletion request and will process it within 30 days.</p>
                <p className="mb-6">A confirmation email has been sent to {email}.</p>
                <p>If you have any questions about your request, please contact us at <a href="mailto:<EMAIL>" className="text-jade-purple hover:underline"><EMAIL></a>.</p>
              </div>
            )}

            <h2 className="text-xl font-semibold mt-8 mb-4 text-white">What Happens Next</h2>
            <p>After submitting your request, we will:</p>
            <ol className="list-decimal pl-6 mt-2 mb-6 space-y-2">
              <li><strong>Identity Verification:</strong> Verify your identity to protect your privacy and prevent unauthorized deletions</li>
              <li><strong>Data Assessment:</strong> Identify all data associated with your account across our systems</li>
              <li><strong>Deletion Process:</strong> Remove your data from active systems, databases, and backups</li>
              <li><strong>Platform Disconnection:</strong> Disconnect your social media integrations and stop data collection</li>
              <li><strong>Confirmation:</strong> Send you a confirmation email when the deletion is complete</li>
            </ol>

            <p className="mb-4"><strong>Timeline:</strong> Most deletion requests are processed within 7-14 business days. Complex cases may take up to 30 days.</p>

            <h2 className="text-xl font-semibold mt-8 mb-4 text-white">What Gets Deleted</h2>
            <p>When you request data deletion, the following will be permanently removed:</p>
            <ul className="list-disc pl-6 mt-2 mb-4 space-y-2">
              <li>Your account profile and login credentials</li>
              <li>All uploaded business knowledge base content (questions, replies, audio, images)</li>
              <li>Conversation history and customer interaction data</li>
              <li>Social media integration tokens and permissions</li>
              <li>Usage analytics and performance metrics</li>
              <li>Billing and subscription information (except as required for tax/legal purposes)</li>
            </ul>

            <p className="mb-6"><strong>Important:</strong> Data deletion is permanent and cannot be undone. You will need to recreate your account and re-upload all content if you wish to use ChhlatBot again.</p>

            <h2 className="text-xl font-semibold mt-8 mb-4 text-white">Partial Deletion Options</h2>
            <p>If you don't want to delete your entire account, you can also:</p>
            <ul className="list-disc pl-6 mt-2 mb-6 space-y-2">
              <li><strong>Disconnect Social Media:</strong> Remove specific platform integrations from your dashboard while keeping your account</li>
              <li><strong>Clear Knowledge Base:</strong> Delete uploaded content while maintaining account structure (contact support)</li>
              <li><strong>Pause Service:</strong> Temporarily disable automated responses without deleting data (contact support)</li>
              <li><strong>Export Data:</strong> Download your data before deletion (contact support for assistance)</li>
            </ul>

            <h2 className="text-xl font-semibold mt-8 mb-4 text-white">Data Retention and Legal Requirements</h2>
            <p>After processing your deletion request, we will remove your personal information from our active systems. However, we may retain certain information for limited periods for the following legal reasons:</p>
            <ul className="list-disc pl-6 mt-2 mb-4 space-y-2">
              <li><strong>Legal Compliance:</strong> Tax records, transaction history as required by law (typically 7 years)</li>
              <li><strong>Dispute Resolution:</strong> Information necessary to resolve ongoing legal disputes</li>
              <li><strong>Terms Enforcement:</strong> Data needed to enforce our terms and prevent abuse</li>
              <li><strong>Backup Systems:</strong> Data in backup systems will be deleted during the next backup cycle (typically within 90 days)</li>
            </ul>

            <p className="mb-4">This retained data is stored securely, access-restricted, and will be deleted once legal requirements are satisfied.</p>

            <p className="mt-8 text-sm text-gray-500">For more information about our data practices, please refer to our <Link href="/privacy" className="text-jade-purple hover:underline">Privacy Policy</Link>.</p>
          </div>
        </div>
      </motion.div>

      <Footer />
    </div>
  )
}