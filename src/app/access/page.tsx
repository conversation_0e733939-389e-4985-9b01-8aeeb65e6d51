'use client'

import { useState, useRef } from 'react'
import { createBrowserClient } from '@supabase/ssr'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { useLanguage } from '@/context/LanguageContext'
import LanguageSwitcher from '@/components/LanguageSwitcher'
// import Footer from '@/components/Footer'

export default function AccessPage() {
  const { t } = useLanguage()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isBlurActive, setIsBlurActive] = useState(false)
  const logoRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // Create Supabase client directly
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setIsBlurActive(true) // Activate blur effect

    // Smooth scroll to logo
    logoRef.current?.scrollIntoView({ behavior: 'smooth' })

    try {
      // Add a small delay to allow the scroll and blur to be visible
      await new Promise(resolve => setTimeout(resolve, 300))

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        setError(error.message)
        setIsBlurActive(false) // Only deactivate blur if there's an error
        setIsLoading(false) // Only stop loading if there's an error
      } else {
        // Keep blur active during navigation
        // Don't deactivate isLoading either
        router.push('/dashboard')
      }
    } catch (err) {
      setError('An unexpected error occurred')
      setIsBlurActive(false) // Deactivate blur on exception
      setIsLoading(false) // Stop loading on exception
    }
  }

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative">
      {/* Language Switcher */}
      <div className="absolute top-4 right-4 z-[70]">
        <LanguageSwitcher />
      </div>

      {/* Blur overlay */}
      {isBlurActive && (
        <div className="fixed inset-0 bg-deep-blue/50 backdrop-blur-md z-50 flex items-center justify-center pointer-events-none">
          <div className="animate-pulse text-white text-xl">{t('access_loading')}</div>
        </div>
      )}

      <div ref={logoRef} className="px-4 py-8 mx-auto flex items-center justify-center relative z-[60]">
        <Link href="/" className="block cursor-pointer">
          <img
            src="/images/white_tran_logo.svg"
            alt="ChhlatBot"
            className="h-10 w-auto cursor-pointer hover:opacity-80 transition-opacity"
          />
        </Link>
      </div>

      <div className="flex-grow flex items-center justify-center px-4 py-12" style={{ marginTop: "-20vh" }}>
        <motion.div
          className="w-full max-w-md p-8 rounded-xl shadow-xl"
          style={{ border: '1px solid rgba(134, 107, 255, 0.3)',
            backgroundColor: 'rgba(255, 255, 255, 0.025)',
            boxShadow: '0 0 15px rgba(134, 107, 255, 0.5)'
           }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-2xl sm:text-3xl font-bold mb-6 font-title text-center">
            {t('access_dashboard_title')}
          </h1>

          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-zinc-300 mb-2">
                {t('access_email_label')}
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 rounded-lg bg-black/5 border border-white/30 text-white placeholder-white/80 focus:outline-none focus:ring-1 focus:ring-jade-purple/80 focus:border-transparent"
                placeholder={t('access_email_placeholder')}
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-zinc-300 mb-2">
                {t('access_password_label')}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 rounded-lg bg-black/5 border border-white/30 text-white placeholder-white/80 focus:outline-none focus:ring-1 focus:ring-jade-purple/80 focus:border-transparent"
                placeholder={t('access_password_placeholder')}
                required
              />
            </div>

            {error && (
              <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-500 text-sm">
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? t('access_loading') : t('access_button')}
            </button>

            {/* <div className="text-center text-sm text-zinc-500">
              <Link href="/forgot-password" className="text-violet-500 hover:text-violet-400">
                Forgot your password?
              </Link>
            </div> */}
          </form>
        </motion.div>
      </div>

      {/* <Footer /> */}
    </div>
  )
}
