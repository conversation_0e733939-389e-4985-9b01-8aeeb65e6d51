import './globals.css'
import type { Metadata } from 'next'
import { fontVariables } from '@/styles/fonts'
import { AuthProvider } from '@/context/AuthContext'
import { LanguageProvider } from '@/context/LanguageContext'
import { LoadingProvider } from '@/context/LoadingContext'

export const metadata: Metadata = {
  title: 'ChhlatBot - Intelligent Conversations for Social Media',
  description: 'Automate customer engagement across all your social platforms with ChhlatBot.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="kh" className={`scroll-smooth ${fontVariables}`}>
      <head>
        {/* Cloudflare Web Analytics will be automatically injected via dashboard */}
      </head>
      <body className="antialiased">
        <AuthProvider>
          <LanguageProvider>
            <LoadingProvider>
              {children}
            </LoadingProvider>
          </LanguageProvider>
        </AuthProvider>
      </body>
    </html>
  )
}