'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useLanguage } from '@/context/LanguageContext'

export default function PaymentCancelPage() {
  const [countdown, setCountdown] = useState(5)
  const router = useRouter()
  const { t } = useLanguage()

  useEffect(() => {
    // Log that the cancel page was loaded
    console.log('Payment cancel page loaded');

    // Log URL parameters
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      console.log('Cancel page URL parameters:');
      url.searchParams.forEach((value, key) => {
        console.log(`${key}: ${value}`);
      });
    }

    // Auto-redirect to dashboard after 5 seconds
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          router.push('/dashboard/plans')
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [router])

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md p-8 rounded-xl relative overflow-hidden"
        style={{
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 0 20px rgba(255, 0, 0, 0.2), inset 0 0 20px rgba(255, 0, 0, 0.1)'
        }}
      >
        <div className="absolute inset-0 rounded-xl overflow-hidden" style={{
          pointerEvents: 'none'
        }}></div>

        <div className="text-center">
          <div className="w-20 h-20 bg-red-500/20 rounded-full mx-auto flex items-center justify-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>

          <h1 className="text-2xl font-bold mb-4 font-title">{t('payment_cancelled_title')}</h1>
          <p className="mb-6 text-zinc-300 font-body">{t('payment_cancelled_message')}</p>

          <div className="mb-6 p-3 rounded-lg" style={{
            backgroundColor: 'rgba(255, 0, 0, 0.1)',
            border: '1px solid rgba(255, 0, 0, 0.3)'
          }}>
            <p className="text-lg font-semibold">
              {t('redirecting_in')} <span className="text-red-500">{countdown}</span> {t('seconds')}
            </p>
          </div>

          <div className="flex justify-center">
            <Link href="/dashboard/plans">
              <motion.button
                className="btn-secondary px-6 py-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {t('try_again')}
              </motion.button>
            </Link>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
