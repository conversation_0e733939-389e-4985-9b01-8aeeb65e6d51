export default function NotFound() {
  return (
    <div className="min-h-screen bg-deep-blue flex flex-col items-center justify-center px-4 py-12">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Logo Header */}
      <div className="mb-8">
        <a href="/">
          <img
            src="/images/white_tran_logo.svg"
            alt="ChhlatBot"
            className="h-10 w-auto"
          />
        </a>
      </div>

      <div className="w-full max-w-md p-8 rounded-xl border border-white/20 shadow-xl text-center"
        style={{
          boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.30)'
        }}
      >
        <div className="w-20 h-20 mx-auto mb-6 bg-jade-purple/20 rounded-full flex items-center justify-center">
          <svg
            className="w-10 h-10 text-jade-purple"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>

        <h1 className="text-3xl sm:text-4xl font-bold mb-4 font-title">
          <span className="text-white">404</span> <span className="gradient-text">Not Found</span>
        </h1>

        <p className="mb-8 text-gray-300 font-body">
          Oops! The page you're looking for doesn't exist or has been moved.
        </p>

        <div className="space-y-4">
          <a
            href="/"
            className="block w-full py-3 px-6 text-center bg-jade-purple text-white font-medium rounded-lg hover:bg-opacity-90 transition-all duration-200"
          >
            Return to Homepage
          </a>
        </div>
      </div>
    </div>
  )
}
