import { createServerComponentSupabase } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Server-side authentication check
  const supabase = await createServerComponentSupabase()
  const { data, error } = await supabase.auth.getUser()

  // Only allow access if user is admin
  const adminEmail = process.env.ADMIN_USER
  if (error || !data.user || !adminEmail || data.user.email !== adminEmail) {
    redirect('/access')
  }

  return (
    <div className="min-h-screen bg-black">
      <header className="bg-black border-b border-zinc-800 py-4">
        <div className="container mx-auto px-4">
          <h1 className="text-xl md:text-2xl font-bold font-title">
            <span className="gradient-text">Chhlat</span><span className="text-white">Bot</span> <span className="text-white">Admin</span>
          </h1>
        </div>
      </header>
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  )
}