'use client'

import { useState, useEffect } from 'react'

export default function AdminResetPassword() {
  const [email, setEmail] = useState('')
  const [userId, setUserId] = useState('')
  const [userEmail, setUserEmail] = useState('')
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')
  const [isMounted, setIsMounted] = useState(false)
  const [resetComplete, setResetComplete] = useState(false)
  // State to track which field was just copied
  const [copiedField, setCopiedField] = useState<string | null>(null)
  
  useEffect(() => {
    setIsMounted(true)
  }, [])
  
  // Function to copy text to clipboard
  const copyToClipboard = (text: string, field: string) => {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(text).then(() => {
        setCopiedField(field)
        // Reset the copied field after 2 seconds
        setTimeout(() => setCopiedField(null), 2000)
      })
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        document.execCommand('copy')
        setCopiedField(field)
        // Reset the copied field after 2 seconds
        setTimeout(() => setCopiedField(null), 2000)
      } catch (err) {
        console.error('Failed to copy text: ', err)
      }
      
      document.body.removeChild(textArea)
    }
  }
  
  // Generate a secure random password
  const generatePassword = (length = 32) => {
    const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+"
    let password = ""
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length)
      password += charset[randomIndex]
    }
    return password
  }
  
  // Find user by email username
  const findUserByEmail = async () => {
    if (!email.trim()) {
      setMessage('Please enter an email address')
      setStatus('error')
      return
    }
    
    setStatus('loading')
    setMessage('')
    setResetComplete(false)
    
    try {
      // Call our API endpoint to find the user by email
      const response = await fetch('/api/admin/reset-password/find-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to find user')
      }
      
      // Set the user information
      setUserId(data.userId)
      // Set email and username from what user entered
      setUserEmail(email)
      setUsername(email.split('@')[0])
      
      // Generate a new password
      const newPassword = generatePassword()
      setPassword(newPassword)
      setStatus('success')
      
    } catch (error: any) {
      console.error('Error finding user:', error)
      setMessage(error.message || 'An error occurred while searching for the user')
      setStatus('error')
    }
  }
  
  // Reset user password
  const resetPassword = async () => {
    if (!userId || !password) {
      setMessage('Missing user ID or password')
      setStatus('error')
      return
    }
    
    setStatus('loading')
    setMessage('')
    
    try {
      // Call our API endpoint to update the password
      const response = await fetch('/api/admin/reset-password/update-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId, 
          password
        }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to reset password')
      }
      
      setMessage('Password has been successfully reset')
      setStatus('success')
      setResetComplete(true)
    } catch (error: any) {
      console.error('Error resetting password:', error)
      setMessage(error.message || 'Failed to reset password')
      setStatus('error')
    }
  }
  
  if (!isMounted) {
    return null // Avoid rendering anything on server
  }
  
  return (
    <div className="max-w-md mx-auto bg-zinc-900 rounded-xl p-6 border border-zinc-800">
      <h1 className="text-2xl font-bold mb-6 font-title text-white">Admin Password Reset</h1>
      
      <div className="mb-6">
        <label htmlFor="email" className="block text-sm font-medium text-zinc-300 mb-2">
          User Email
        </label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full px-4 py-3 rounded-lg bg-zinc-800 border border-zinc-700 text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-jade-purple focus:border-transparent"
          placeholder="Enter user email"
        />
      </div>
      
      <div className="text-xs text-zinc-400 mb-4">
        Note: Only the part before @ will be used to find the user in the clients table
      </div>
      
      <button
        onClick={findUserByEmail}
        disabled={status === 'loading'}
        className="w-full py-3 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed mb-6"
      >
        {status === 'loading' ? 'Searching...' : 'Find User'}
      </button>
      
      {userId && password && (
        <div className="mb-6">
          {username && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-zinc-300 mb-2">
                Username
              </label>
              <div className="w-full px-4 py-3 rounded-lg bg-zinc-800 border border-zinc-700 text-white overflow-x-auto">
                {username}
              </div>
            </div>
          )}
          
          {userEmail && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-zinc-300 mb-2">
                Email
              </label>
              <div className="w-full px-4 py-3 rounded-lg bg-zinc-800 border border-zinc-700 text-white overflow-x-auto flex items-center justify-between">
                <span>{userEmail}</span>
                <button
                  onClick={() => copyToClipboard(userEmail, 'email')}
                  className="ml-2 text-zinc-400 hover:text-jade-purple transition-colors focus:outline-none"
                  title="Copy email to clipboard"
                >
                  {copiedField === 'email' ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                      <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                  )}
                </button>
              </div>
            </div>
          )}
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-zinc-300 mb-2">
              User ID
            </label>
            <div className="w-full px-4 py-3 rounded-lg bg-zinc-800 border border-zinc-700 text-zinc-400 overflow-x-auto flex items-center justify-between">
              <span>{userId}</span>
              <button
                onClick={() => copyToClipboard(userId, 'userId')}
                className="ml-2 text-zinc-400 hover:text-jade-purple transition-colors focus:outline-none"
                title="Copy user ID to clipboard"
              >
                {copiedField === 'userId' ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                )}
              </button>
            </div>
          </div>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-zinc-300 mb-2">
              New Password
            </label>
            <div className="w-full px-4 py-3 rounded-lg bg-zinc-800 border border-zinc-700 text-zinc-400 font-mono overflow-x-auto flex items-center justify-between">
              <span>{password}</span>
              <button
                onClick={() => copyToClipboard(password, 'password')}
                className="ml-2 text-zinc-400 hover:text-jade-purple transition-colors focus:outline-none"
                title="Copy password to clipboard"
              >
                {copiedField === 'password' ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                )}
              </button>
            </div>
          </div>
          
          {!resetComplete && (
            <button
              onClick={resetPassword}
              disabled={status === 'loading'}
              className="w-full py-3 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {status === 'loading' ? 'Resetting...' : 'Reset Password'}
            </button>
          )}
          
          {resetComplete && (
            <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20 text-green-500">
              <h3 className="font-semibold mb-2">Password Reset Successful</h3>
              <p>The password for {userEmail} has been reset successfully.</p>
            </div>
          )}
        </div>
      )}
      
      {message && !resetComplete && (
        <div className={`p-4 rounded-lg ${
          status === 'error' ? 'bg-red-500/10 border border-red-500/20 text-red-500' : 
          'bg-green-500/10 border border-green-500/20 text-green-500'
        } text-sm mt-4`}>
          {message}
        </div>
      )}
    </div>
  )
}