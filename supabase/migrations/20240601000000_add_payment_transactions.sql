-- Create payment_transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id TEXT NOT NULL REFERENCES clients(client_id),
  transaction_id TEXT NOT NULL,
  amount TEXT,
  currency TEXT,
  status TEXT,
  status_message TEXT,
  approval_code TEXT,
  payment_method TEXT,
  plan_type TEXT,
  billing_cycle INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index on client_id for faster lookups
CREATE INDEX IF NOT EXISTS payment_transactions_client_id_idx ON payment_transactions(client_id);

-- Add index on transaction_id for faster lookups
CREATE INDEX IF NOT EXISTS payment_transactions_transaction_id_idx ON payment_transactions(transaction_id);

-- Add RLS policies for payment_transactions table
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own payment transactions
CREATE POLICY "Users can view their own payment transactions" ON payment_transactions
  FOR SELECT
  USING (
    client_id IN (
      SELECT client_id FROM clients WHERE auth_id = auth.uid()
    )
  );

-- Only allow service role to insert/update payment transactions
CREATE POLICY "Service role can insert payment transactions" ON payment_transactions
  FOR INSERT
  WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "Service role can update payment transactions" ON payment_transactions
  FOR UPDATE
  USING (auth.role() = 'service_role');
