-- Add Telegram Business fields to client_credentials table
ALTER TABLE client_credentials 
ADD COLUMN IF NOT EXISTS tg_biz_id TEXT,
ADD COLUMN IF NOT EXISTS tg_biz_status INTEGER DEFAULT 0;

-- Add index on tg_biz_id for faster lookups
CREATE INDEX IF NOT EXISTS client_credentials_tg_biz_id_idx ON client_credentials(tg_biz_id);

-- Add index on tg_biz_status for faster status queries
CREATE INDEX IF NOT EXISTS client_credentials_tg_biz_status_idx ON client_credentials(tg_biz_status);
