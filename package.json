{"name": "chhlat-bot", "version": "0.1.0", "private": true, "scripts": {"dev": "kill -9 $(lsof -t -i:3000) 2>/dev/null || true && next dev", "build": "next build", "start": "next start -H 0.0.0.0 -p 3000", "lint": "next lint"}, "dependencies": {"@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/uuid": "^10.0.0", "@upstash/redis": "^1.34.8", "browser-image-compression": "^2.0.2", "framer-motion": "^10.16.4", "nanoid": "^5.1.5", "next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-h5-audio-player": "^3.10.0-rc.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.5.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.11.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}}