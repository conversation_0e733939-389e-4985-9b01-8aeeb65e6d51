/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-inter)', 'Inter', 'Inter Placeholder', 'sans-serif'],
        title: ['var(--font-plus-jakarta-sans)', 'Plus Jakarta Sans', 'Plus Jakarta Sans Placeholder', 'sans-serif'],
      },
      colors: {
        'jade-black': '#000000',
        'jade-white': '#FFFFFF',
        'jade-purple': '#673de6',
        'jade-purple-dark': '#532cc7',
        'jade-gray': '#CCCCCC',
        'deep-blue': '#03011f',
      },
      backgroundImage: {
        'gradient-purple': 'linear-gradient(to right, #866bff, #7559e8)',
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'fadeIn': 'fadeIn 0.5s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        }
      }
    },
  },
  plugins: [],
}