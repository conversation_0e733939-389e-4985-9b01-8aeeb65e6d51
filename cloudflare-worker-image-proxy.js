/**
 * <PERSON><PERSON><PERSON><PERSON> Worker for ChhlatBot Image Optimization
 *
 * This worker proxies Supabase images and applies Cloudflare's image optimization.
 * Provides WebP/AVIF conversion, resizing, and global CDN caching.
 *
 * Deploy this to: https://your-worker.your-domain.workers.dev
 */

export default {
  async fetch(request, env, ctx) {
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Max-Age': '86400',
        }
      })
    }

    // Only allow GET requests for images
    if (request.method !== 'GET') {
      return new Response('Method not allowed', {
        status: 405,
        headers: {
          'Access-Control-Allow-Origin': '*',
        }
      })
    }

    const url = new URL(request.url)

    // Extract optimization parameters
    const imageUrl = url.searchParams.get('url')
    const width = Math.min(parseInt(url.searchParams.get('w') || '800'), 2048)
    const height = Math.min(parseInt(url.searchParams.get('h') || '600'), 2048)
    const quality = Math.min(Math.max(parseInt(url.searchParams.get('q') || '85'), 1), 100)
    const format = url.searchParams.get('f') || 'webp'

    // Validate required parameters
    if (!imageUrl) {
      return new Response('Missing "url" parameter', {
        status: 400,
        headers: { 'Access-Control-Allow-Origin': '*' }
      })
    }

    // Security: Only allow your Supabase domain
    try {
      const imageUrlObj = new URL(imageUrl)
      const allowedDomains = [
        'ijmgtghlpmqfdywtlbnn.supabase.co',
        // Add other domains if needed
      ]

      const isAllowed = allowedDomains.some(domain =>
        imageUrlObj.hostname === domain ||
        imageUrlObj.hostname.endsWith('.supabase.co')
      )

      if (!isAllowed) {
        return new Response(`Domain "${imageUrlObj.hostname}" not allowed`, {
          status: 403,
          headers: { 'Access-Control-Allow-Origin': '*' }
        })
      }
    } catch (e) {
      return new Response('Invalid image URL format', {
        status: 400,
        headers: { 'Access-Control-Allow-Origin': '*' }
      })
    }

    try {
      // Fetch the original image from Supabase
      const imageResponse = await fetch(imageUrl, {
        headers: {
          'User-Agent': 'ChhlatBot-Image-Optimizer/1.0',
          'Accept': 'image/*',
        },
        cf: {
          // Cache the original image for 1 hour
          cacheTtl: 3600,
          cacheEverything: true,
        }
      })

      if (!imageResponse.ok) {
        return new Response(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`, {
          status: imageResponse.status,
          headers: { 'Access-Control-Allow-Origin': '*' }
        })
      }

      // Apply Cloudflare image optimization
      const optimizedResponse = new Response(imageResponse.body, {
        headers: {
          'Content-Type': `image/${format}`,
          'Cache-Control': 'public, max-age=31536000, immutable', // Cache for 1 year
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
          'X-Optimized-By': 'ChhlatBot-Cloudflare-Worker',
          'X-Original-Size': imageResponse.headers.get('content-length') || 'unknown',
        }
      })

      // Use Cloudflare's image resizing service
      return new Response(optimizedResponse.body, {
        headers: optimizedResponse.headers,
        cf: {
          image: {
            width: width,
            height: height,
            quality: quality,
            format: format === 'auto' ? 'webp' : format,
            fit: 'scale-down',
            metadata: 'none', // Strip metadata for smaller files
            sharpen: 1.0, // Slight sharpening for better quality
          }
        }
      })

    } catch (error) {
      console.error('Image optimization error:', error)
      return new Response(`Image processing failed: ${error.message}`, {
        status: 500,
        headers: { 'Access-Control-Allow-Origin': '*' }
      })
    }
  }
}

/**
 * Usage Example:
 *
 * Original Supabase URL:
 * https://ijmgtghlpmqfdywtlbnn.supabase.co/.../photo.jpg
 *
 * Optimized via Worker:
 * https://your-worker.your-domain.workers.dev/?url=https://ijmgtghlpmqfdywtlbnn.supabase.co/.../photo.jpg&w=800&h=600&q=90&f=webp
 *
 * Deploy Instructions:
 * 1. Go to Cloudflare Dashboard → Workers & Pages
 * 2. Create new Worker
 * 3. Paste this code
 * 4. Deploy to your custom domain or workers.dev subdomain
 * 5. Update your image optimization utility to use this worker
 */
